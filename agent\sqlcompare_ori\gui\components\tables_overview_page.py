#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比对列表概览页面组件
"""
import os
import sys
import tkinter as tk
from tkinter import ttk
from .base_page import BasePage
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_DIR)


class TablesOverviewPage(BasePage):
    """比对列表概览页面"""
    
    def __init__(self, parent, app_instance=None):
        super().__init__(parent, app_instance)
        self.table_data = []
    
    def _create_ui(self):
        """创建比对列表界面"""
        self.frame = ttk.Frame(self.parent)

        # 表格区域
        self._create_table_section(self.frame)

        # 加载真实数据
        self._load_table_data()    

    
    def _create_table_section(self, parent):
        """创建表格区域 - 完全填满工作区域，无边距"""
        # 直接创建表格容器，完全填满父容器
        table_container = ttk.Frame(parent)
        table_container.pack(fill=tk.BOTH, expand=True)
        
        # 定义表格列
        columns = ("表名", "说明", "状态", "差异数量", "耗时(秒)", "最后更新")
        # 移除固定高度，让表格自适应填满区域
        self.table_tree = ttk.Treeview(table_container, columns=columns, show="headings")
        
        # 设置列标题和宽度
        column_widths = {"表名": 150, "说明": 200, "状态": 100, "差异数量": 100, "耗时(秒)": 100, "最后更新": 150}
        for col in columns:
            self.table_tree.heading(col, text=col, command=lambda c=col: self._sort_by_column(c))
            self.table_tree.column(col, width=column_widths.get(col, 120), anchor="center")
        
        # 添加智能滚动条 - 只在需要时显示
        self.v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.table_tree.yview)
        self.h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.table_tree.xview)
        self.table_tree.configure(yscrollcommand=self._on_v_scroll, xscrollcommand=self._on_h_scroll)

        # 布局 - 表格先占满空间
        self.table_tree.grid(row=0, column=0, sticky="nsew")
        # 滚动条初始隐藏，根据需要动态显示
        
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.table_tree.bind('<Configure>', self._on_tree_configure)
        # 双击查看详情
        self.table_tree.bind("<Double-1>", self._on_table_double_click)
        
        # 配置行颜色 - 添加交替行背景色
        self.table_tree.tag_configure("odd_row", background="#f0f8ff")   # 浅蓝色
        self.table_tree.tag_configure("even_row", background="white")    # 白色
        self.table_tree.tag_configure("completed", background="#e8f5e8")
        self.table_tree.tag_configure("pending", background="#fff8dc")
        self.table_tree.tag_configure("error", background="#ffe8e8")
        self.table_tree.tag_configure("running", background="#e8f4fd")
    
    def _load_table_data(self):
        """加载比对列表数据"""
        # 确保在UI创建后加载数据
        if not self.config_manager.config:
            # 如果没有加载配置，可以尝试自动加载
            config_files = self.config_manager.auto_discover_configs()
            if config_files:
                self.config_manager.load_config(config_files[0])
            else:
                self.app._log_message("未找到任何配置文件。", "WARNING")
                return
        try:
            if not self.config_manager or not hasattr(self.config_manager, 'get_comparison_tables'):
                return

            # 获取表配置
            tables = self.config_manager.get_comparison_tables()
            if not tables:
                return

            # 转换为界面数据格式
            real_data = []
            for table in tables:
                table_id = table.get('table_id', '')
                remark = table.get('remark', '')

                if table_id:
                    # 使用真实的表信息
                    display_name = remark if remark else table_id.replace('_', ' ').title()

                    status = "就绪"
                    diff_count = "0"
                    duration = "0.0"
                    last_update = "-"
                    real_data.append((table_id, display_name, status, diff_count, duration, last_update))

            if real_data:
                self._populate_table_data(real_data)

        except Exception as e:
            print(f"❌ 加载表数据失败: {e}")

    def _get_db_info(self, db_section):
        """获取数据库信息"""
        if not self.config or not self.config.has_section(db_section):
            return None

        return {
            'name': self.config.get(db_section, 'NAME', fallback=''),
            'type': self.config.get(db_section, 'TYPE', fallback=''),
            'ip': self.config.get(db_section, 'IP', fallback=''),
            'port': self.config.get(db_section, 'PORT', fallback=''),
            'schema': self.config.get(db_section, 'SCHEMA', fallback='')
        }

    def _populate_table_data(self, data_list):
        """填充表格数据"""
        self.table_data = []

        for index, data in enumerate(data_list):
            table_name, description, status, diff_count, duration, last_update = data

            # 确定标签 - 优先使用状态标签，否则使用交替行标签
            tags = []
            if status == "已完成":
                tags.append("completed")
            elif status == "进行中":
                tags.append("running")
            elif status == "待处理":
                tags.append("pending")
            elif status == "错误":
                tags.append("error")
            else:
                # 如果没有特殊状态，使用交替行背景色
                if index % 2 == 0:
                    tags.append("odd_row")  # 偶数行 - 浅蓝
                else:
                    tags.append("even_row") # 奇数行 - 白色

            item_id = self.table_tree.insert("", tk.END, values=data, tags=tuple(tags))

            self.table_data.append({
                "item_id": item_id,
                "table_name": table_name,
                "description": description,
                "status": status,
                "diff_count": int(diff_count) if diff_count.isdigit() else 0,
                "duration": float(duration) if duration.replace(".", "").isdigit() else 0.0,
                "last_update": last_update
            })

    def _load_sample_data(self):
        """加载示例数据（作为备用）"""
        print("⚠️ 使用示例数据作为备用")
        sample_data = [
            ("user_table", "用户信息表", "已完成", "5", "2.3", "2024-01-15 10:30:25"),
            ("order_table", "订单信息表", "已完成", "12", "4.7", "2024-01-15 10:32:18"),
            ("product_table", "产品信息表", "进行中", "0", "0.0", "2024-01-15 10:35:00"),
            ("customer_table", "客户信息表", "待处理", "0", "0.0", "-"),
            ("inventory_table", "库存信息表", "错误", "0", "0.0", "2024-01-15 10:28:45"),
            ("payment_table", "支付信息表", "已完成", "3", "1.8", "2024-01-15 10:33:12"),
            ("category_table", "分类信息表", "已完成", "0", "0.9", "2024-01-15 10:31:05"),
            ("supplier_table", "供应商信息表", "待处理", "0", "0.0", "-")
        ]

        self._populate_table_data(sample_data)
        

    
    def _sort_by_column(self, column):
        """按列排序"""
        # 获取当前数据
        data = [(self.table_tree.set(item, column), item) for item in self.table_tree.get_children("")]
        
        # 排序
        try:
            # 尝试数值排序
            data.sort(key=lambda x: float(x[0]) if x[0].replace(".", "").replace("-", "").isdigit() else float('inf'))
        except:
            # 字符串排序
            data.sort(key=lambda x: x[0])
        
        # 重新排列
        for index, (val, item) in enumerate(data):
            self.table_tree.move(item, "", index)
        
        self._log_message(f"按 {column} 列排序")
    
    def _on_table_double_click(self, event):
        """表格双击事件"""
        selection = self.table_tree.selection()
        if selection:
            item = selection[0]
            values = self.table_tree.item(item, "values")
            table_name = values[0]
            
            self._log_message(f"双击查看表: {table_name}")
            
            # 这里应该切换到单表比对结果页面
            if self.app and hasattr(self.app, '_show_table_comparison_page'):
                self.app._show_table_comparison_page(f"table_{table_name}")

    def show(self):
        """显示页面"""

        # 调用父类的show方法
        super().show()    


    def refresh(self):
        """刷新页面数据"""
        # 重新加载示例数据
        for item in self.table_tree.get_children():
            self.table_tree.delete(item)
        self._load_table_data()

    def get_data(self):
        """获取页面数据"""
        return {
            "table_data": self.table_data,
            "statistics": {
                "total_tables": len(self.table_data),
                "completed_tables": len([t for t in self.table_data if t["status"] == "已完成"]),
                "total_differences": sum(t["diff_count"] for t in self.table_data)
            }
        }

    def update_table_status(self, table_name, status, diff_count=0, duration=0.0):
        """更新表的比对状态"""
        for data in self.table_data:
            if data["table_name"] == table_name:
                data["status"] = status
                data["diff_count"] = diff_count
                data["duration"] = duration
                data["last_update"] = "刚刚"

                # 确定标签
                tag = ""
                if status == "已完成":
                    tag = "completed"
                elif status == "进行中":
                    tag = "running"
                elif status == "待处理":
                    tag = "pending"
                elif status == "错误":
                    tag = "error"

                # 更新表格显示
                values = (data["table_name"], data["description"], status,
                         str(diff_count), str(duration), data["last_update"])
                self.table_tree.item(data["item_id"], values=values, tags=(tag,))
                break

    def _on_v_scroll(self, *args):
        """垂直滚动条回调，智能显示/隐藏滚动条"""
        # 更新滚动条位置
        self.v_scrollbar.set(*args)

        # 检查是否需要显示垂直滚动条
        if args[0] == '0.0' and args[1] == '1.0':
            # 内容完全可见，隐藏滚动条
            self.v_scrollbar.grid_remove()
        else:
            # 内容超出，显示滚动条
            self.v_scrollbar.grid(row=0, column=1, sticky="ns")

    def _on_h_scroll(self, *args):
        """水平滚动条回调，智能显示/隐藏滚动条"""
        # 更新滚动条位置
        self.h_scrollbar.set(*args)

        # 检查是否需要显示水平滚动条
        if args[0] == '0.0' and args[1] == '1.0':
            # 内容完全可见，隐藏滚动条
            self.h_scrollbar.grid_remove()
        else:
            # 内容超出，显示滚动条
            self.h_scrollbar.grid(row=1, column=0, sticky="ew")

    def _on_tree_configure(self, event):
        """表格配置变化时，重新检查滚动条需求"""
        # 延迟执行，确保布局完成
        self.table_tree.after_idle(self._update_scrollbars)

    def _update_scrollbars(self):
        """更新滚动条显示状态"""
        try:
            # 获取当前滚动位置
            v_pos = self.table_tree.yview()
            h_pos = self.table_tree.xview()

            # 更新滚动条状态
            self._on_v_scroll(*v_pos)
            self._on_h_scroll(*h_pos)
        except:
            # 忽略可能的错误
            pass



