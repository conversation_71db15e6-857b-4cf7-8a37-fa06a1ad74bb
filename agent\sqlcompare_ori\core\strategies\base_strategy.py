# core/strategies/base_strategy.py
"""
比对策略基类
定义所有比对策略的通用接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable
from connectors.base_connector import BaseConnector
from reporters.base_reporter import BaseReporter


class ComparisonStrategy(ABC):
    """比对策略抽象基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化比对策略
        
        Args:
            config: 策略配置参数
        """
        self.config = config or {}
    
    @abstractmethod
    def compare(
        self,
        source_a: BaseConnector,
        source_b: BaseConnector,
        reporter: BaseReporter,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        执行数据比对
        
        Args:
            source_a: 数据源A的连接器
            source_b: 数据源B的连接器
            reporter: 差异报告器
            progress_callback: 进度回调函数
            
        Returns:
            比对结果统计信息
        """
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass
    
    @abstractmethod
    def estimate_memory_usage(self, record_count_a: int, record_count_b: int) -> int:
        """
        估算内存使用量（字节）
        
        Args:
            record_count_a: 数据源A的记录数
            record_count_b: 数据源B的记录数
            
        Returns:
            估算的内存使用量（字节）
        """
        pass
    
    @abstractmethod
    def requires_sorting(self) -> bool:
        """是否需要数据排序"""
        pass
