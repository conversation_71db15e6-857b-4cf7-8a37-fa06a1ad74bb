# connectors/asynchronous/base_connector.py
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import AsyncIterator, Optional, Dict, Any

from core.models import Record


class AsyncBaseConnector(ABC):
    """异步数据源连接器抽象基类"""
    
    def __init__(self, chunk_size: int = 1000, read_timeout: float = 30.0):
        """
        初始化异步连接器
        
        Args:
            chunk_size: 分块读取大小
            read_timeout: 读取超时时间
        """
        self.chunk_size = chunk_size
        self.read_timeout = read_timeout
        self.logger = logging.getLogger(self.__class__.__name__)
        self._connection = None
        self._is_connected = False
        
        # 性能统计
        self.stats = {
            'total_records_read': 0,
            'total_chunks_read': 0,
            'total_read_time': 0.0,
            'connection_time': 0.0,
            'last_error': None
        }
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        建立到数据源的异步连接
        
        Returns:
            连接是否成功
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """
        关闭到数据源的连接
        
        Returns:
            断开是否成功
        """
        pass
    
    @abstractmethod
    async def fetch_data_async(self) -> AsyncIterator[Record]:
        """
        异步获取数据，返回异步迭代器
        
        Yields:
            Record: 数据记录
        """
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """
        测试连接是否有效
        
        Returns:
            连接是否有效
        """
        pass
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
    
    def __aiter__(self):
        """异步迭代器协议"""
        return self.fetch_data_async()
    
    async def get_connection_info(self) -> Dict[str, Any]:
        """
        获取连接信息
        
        Returns:
            连接信息字典
        """
        return {
            'is_connected': self._is_connected,
            'chunk_size': self.chunk_size,
            'read_timeout': self.read_timeout,
            'stats': self.stats.copy()
        }
    
    async def set_chunk_size(self, chunk_size: int):
        """
        动态设置分块大小
        
        Args:
            chunk_size: 新的分块大小
        """
        if chunk_size > 0:
            self.chunk_size = chunk_size
            self.logger.info(f"分块大小已更新为: {chunk_size}")
        else:
            raise ValueError("分块大小必须大于0")
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计字典
        """
        return self.stats.copy()
    
    def _update_stats(self, records_count: int, read_time: float):
        """
        更新性能统计
        
        Args:
            records_count: 读取的记录数
            read_time: 读取耗时
        """
        self.stats['total_records_read'] += records_count
        self.stats['total_chunks_read'] += 1
        self.stats['total_read_time'] += read_time
    
    def _record_error(self, error: Exception):
        """
        记录错误信息
        
        Args:
            error: 异常对象
        """
        self.stats['last_error'] = str(error)
        self.logger.error(f"连接器错误: {error}")


class AsyncDatabaseConnector(AsyncBaseConnector):
    """异步数据库连接器基类"""
    
    def __init__(self, db_config: Dict[str, Any], query: str, **kwargs):
        """
        初始化异步数据库连接器
        
        Args:
            db_config: 数据库配置
            query: SQL查询语句
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        self.db_config = db_config
        self.query = query
        self._validate_config()
    
    def _validate_config(self):
        """验证数据库配置，子类可以重写此方法以实现更具体的验证。"""
        required_fields = ['ip', 'port', 'database']
        for field in required_fields:
            if field not in self.db_config:
                raise ValueError(f"数据库配置缺少必需字段: {field}")
    
    @abstractmethod
    async def fetch_data_async(self) -> AsyncIterator[Record]:
        """
        异步获取数据，返回一个异步迭代器。
        具体的实现（例如分页或流式）由子类决定。

        Yields:
            Record: 数据记录对象
        """
        # 此处需要yield以使该方法成为一个异步生成器，但由于是抽象方法，它不应有实现。
        if False:
            yield
    
    @abstractmethod
    async def get_record_count(self) -> int:
        """
        获取查询结果的精确记录总数。

        Returns:
            记录总数。
        """


class AsyncFileConnector(AsyncBaseConnector):
    """异步文件连接器基类"""
    
    def __init__(self, file_path: str, **kwargs):
        """
        初始化异步文件连接器
        
        Args:
            file_path: 文件路径
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        self.file_path = file_path
        self._file_handle = None
    
    async def connect(self) -> bool:
        """建立文件连接"""
        try:
            # 这里可以添加文件存在性检查等逻辑
            self._is_connected = True
            return True
        except Exception as e:
            self._record_error(e)
            return False
    
    async def disconnect(self) -> bool:
        """关闭文件连接"""
        try:
            if self._file_handle:
                self._file_handle.close()
                self._file_handle = None
            self._is_connected = False
            return True
        except Exception as e:
            self._record_error(e)
            return False
    
    async def test_connection(self) -> bool:
        """测试文件是否可访问"""
        try:
            import aiofiles
            async with aiofiles.open(self.file_path, 'r') as f:
                await f.read(1)  # 尝试读取一个字符
            return True
        except Exception as e:
            self._record_error(e)
            return False


# 工厂函数
def create_async_connector(connector_type: str, **kwargs) -> AsyncBaseConnector:
    """
    创建异步连接器的工厂函数
    
    Args:
        connector_type: 连接器类型
        **kwargs: 连接器参数
        
    Returns:
        异步连接器实例
    """
    # 这里可以根据connector_type创建不同类型的连接器
    # 具体实现将在各个数据库连接器中完成
    raise NotImplementedError(f"不支持的连接器类型: {connector_type}")
