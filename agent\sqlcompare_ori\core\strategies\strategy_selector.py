# core/strategies/strategy_selector.py
"""
策略选择器
根据数据规模和系统资源智能选择最适合的比对策略
"""
import sys
import os
import logging
from typing import Dict, Any, Optional, Tuple
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.dirname(os.path.dirname(current_dir)))
from .base_strategy import ComparisonStrategy
from .hash_strategy import HashComparisonStrategy
from .merge_strategy import MergeComparisonStrategy
from connectors.base_connector import BaseConnector

logger = logging.getLogger(__name__)


class StrategySelector:
    """策略选择器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化策略选择器
        
        Args:
            config: 选择器配置
        """
        self.config = config or {}
        
        # 默认阈值配置
        self.small_data_threshold = self.config.get('small_data_threshold', 100000)  # 10万条
        self.memory_limit_mb = self.config.get('memory_limit_mb', 2048)  # 2GB内存限制
        self.force_strategy = self.config.get('force_strategy', None)  # 强制使用指定策略
    
    def estimate_record_count(self, connector: BaseConnector) -> int:
        """
        精确获取数据源的记录数量

        Args:
            connector: 数据连接器

        Returns:
            精确的记录数量
        """
        try:
            # 使用COUNT(*)查询获取精确记录数
            logger.info("正在获取数据源精确记录数量...")

            # 检查连接器类型，使用相应的COUNT查询方法
            if hasattr(connector, 'get_record_count'):
                # 如果连接器提供了专门的计数方法
                count = connector.get_record_count()
                return count

            elif hasattr(connector, 'query') and hasattr(connector, 'conn'):
                # 对于DB2Connector，使用COUNT(*)查询
                count = self._count_records_by_sql(connector)
                return count

            else:
                # 回退到迭代计数（适用于文件连接器等）
                logger.info("使用迭代计数方法...")
                count = self._count_records_by_iteration(connector)
                logger.info(f"迭代计数完成，记录数量: {count:,}")
                return count

        except Exception as e:
            logger.warning(f"记录数量获取失败: {e}，使用默认值")
            return self.small_data_threshold + 1  # 默认使用归并策略

    def _count_records_by_sql(self, connector) -> int:
        """
        使用SQL COUNT(*)查询获取记录数量

        Args:
            connector: 数据库连接器

        Returns:
            记录数量
        """
        import ibm_db

        try:
            # 从原始查询构造COUNT查询
            original_query = connector.query.strip()

            # 移除可能的ORDER BY子句（COUNT查询不需要）
            query_upper = original_query.upper()
            order_by_pos = query_upper.rfind("ORDER BY")
            if order_by_pos != -1:
                base_query = original_query[:order_by_pos].strip()
            else:
                base_query = original_query

            # 移除可能的FETCH FIRST限制（COUNT查询需要全部记录）
            fetch_pos = query_upper.rfind("FETCH FIRST")
            if fetch_pos != -1:
                base_query = original_query[:fetch_pos].strip()

            # 构造COUNT查询
            count_query = f"SELECT COUNT(*) AS record_count FROM ({base_query}) AS count_subquery"

            # 执行COUNT查询
            with connector:
                if not connector.conn:
                    connector.connect()

                stmt = ibm_db.exec_immediate(connector.conn, count_query)
                row = ibm_db.fetch_tuple(stmt)

                if row:
                    count = int(row[0])
                    return count
                else:
                    raise Exception("COUNT查询返回空结果")

        except Exception as e:
            logger.warning(f"SQL计数失败: {e}，回退到迭代计数")
            return self._count_records_by_iteration(connector)

    def _count_records_by_iteration(self, connector) -> int:
        """
        通过迭代获取记录数量（回退方法）

        Args:
            connector: 数据连接器

        Returns:
            记录数量
        """
        count = 0
        try:
            with connector:
                for record in connector:
                    count += 1
                    # 每10万条记录输出一次进度
                    if count % 100000 == 0:
                        logger.info(f"迭代计数进度: {count:,} 条记录...")

            return count

        except Exception as e:
            logger.error(f"迭代计数失败: {e}")
            raise
    
    def select_strategy(
        self,
        source_a: BaseConnector,
        source_b: BaseConnector,
        estimate_counts: bool = True
    ) -> Tuple[ComparisonStrategy, Dict[str, Any]]:
        """
        选择最适合的比对策略
        
        Args:
            source_a: 数据源A
            source_b: 数据源B
            estimate_counts: 是否估算记录数量
            
        Returns:
            (选择的策略实例, 选择原因和统计信息)
        """
        logger.info("开始选择比对策略...")
        
        selection_info = {
            'strategy_name': '',
            'reason': '',
            'estimated_count_a': 0,
            'estimated_count_b': 0,
            'estimated_memory_mb': 0,
            'requires_sorting': False
        }
        
        # 如果强制指定策略
        if self.force_strategy:
            if self.force_strategy.lower() == 'hash':
                strategy = HashComparisonStrategy(self.config)
                selection_info.update({
                    'strategy_name': strategy.get_strategy_name(),
                    'reason': '用户强制指定使用哈希策略',
                    'requires_sorting': strategy.requires_sorting()
                })
                logger.info(f"✅ 选择策略: {selection_info['strategy_name']} (强制指定)")
                return strategy, selection_info
            
            elif self.force_strategy.lower() == 'merge':
                strategy = MergeComparisonStrategy(self.config)
                selection_info.update({
                    'strategy_name': strategy.get_strategy_name(),
                    'reason': '用户强制指定使用归并策略',
                    'requires_sorting': strategy.requires_sorting()
                })
                logger.info(f"✅ 选择策略: {selection_info['strategy_name']} (强制指定)")
                return strategy, selection_info
        
        # 智能选择策略
        if estimate_counts:
            # 获取精确数据量
            try:
                logger.info("开始获取数据源记录数量...")
                count_a = self.estimate_record_count(source_a)
                count_b = self.estimate_record_count(source_b)
                logger.info(f"数据量统计: A源 {count_a:,} 条, B源 {count_b:,} 条")
            except Exception as e:
                logger.warning(f"数据量获取失败: {e}，使用默认策略")
                count_a = count_b = self.small_data_threshold + 1
        else:
            # 不获取数据量，使用默认值（适用于快速启动场景）
            logger.info("跳过数据量统计，使用默认策略选择")
            count_a = count_b = self.small_data_threshold + 1
        
        selection_info['estimated_count_a'] = count_a
        selection_info['estimated_count_b'] = count_b
        
        total_records = count_a + count_b

        # 智能策略选择逻辑
        strategy, estimated_memory, reason = self._select_optimal_strategy(
            count_a, count_b, total_records
        )

        selection_info['reason'] = reason
        
        selection_info.update({
            'strategy_name': strategy.get_strategy_name(),
            'estimated_memory_mb': estimated_memory / 1024 / 1024,
            'requires_sorting': strategy.requires_sorting()
        })
        
        # 输出选择结果
        logger.info("=" * 50)
        logger.info("策略选择结果")
        logger.info("=" * 50)
        logger.info(f"选择策略: {selection_info['strategy_name']}")
        logger.info(f"选择原因: {selection_info['reason']}")
        logger.info(f"估算数据量: A源 {count_a:,} 条, B源 {count_b:,} 条")
        logger.info(f"估算内存使用: {selection_info['estimated_memory_mb']:.1f} MB")
        logger.info(f"需要排序: {'是' if selection_info['requires_sorting'] else '否'}")
        logger.info("=" * 50)
        
        return strategy, selection_info

    def _select_optimal_strategy(self, count_a: int, count_b: int, total_records: int) -> tuple:
        """
        选择最优策略的核心逻辑

        Args:
            count_a: A源记录数
            count_b: B源记录数
            total_records: 总记录数

        Returns:
            (策略实例, 估算内存, 选择原因)
        """
        # 数据量分析
        max_single_source = max(count_a, count_b)
        min_single_source = min(count_a, count_b)
        size_ratio = max_single_source / min_single_source if min_single_source > 0 else 1

        logger.debug(f"数据量分析: 总计{total_records:,}, 最大源{max_single_source:,}, 最小源{min_single_source:,}, 比例{size_ratio:.2f}")

        # 策略候选列表
        candidates = []

        # 1. 评估哈希策略
        hash_strategy = HashComparisonStrategy(self.config)
        hash_memory = hash_strategy.estimate_memory_usage(count_a, count_b)
        hash_memory_mb = hash_memory / 1024 / 1024

        if hash_memory_mb <= self.memory_limit_mb:
            # 哈希策略的优势评分
            hash_score = self._calculate_hash_strategy_score(
                total_records, hash_memory_mb, size_ratio
            )
            candidates.append({
                'strategy': hash_strategy,
                'memory_mb': hash_memory_mb,
                'score': hash_score,
                'name': 'hash',
                'reason': f'数据量{total_records:,}条，内存需求{hash_memory_mb:.1f}MB，无需排序'
            })

        # 2. 评估归并策略
        merge_strategy = MergeComparisonStrategy(self.config)
        merge_memory = merge_strategy.estimate_memory_usage(count_a, count_b)
        merge_memory_mb = merge_memory / 1024 / 1024

        merge_score = self._calculate_merge_strategy_score(
            total_records, merge_memory_mb, size_ratio
        )
        candidates.append({
            'strategy': merge_strategy,
            'memory_mb': merge_memory_mb,
            'score': merge_score,
            'name': 'merge',
            'reason': f'数据量{total_records:,}条，内存需求{merge_memory_mb:.1f}MB，流式处理'
        })

        # 3. 选择最优策略
        if not candidates:
            # 回退到归并策略
            return merge_strategy, merge_memory, "内存限制，回退到归并策略"

        # 按评分排序，选择最高分的策略
        best_candidate = max(candidates, key=lambda x: x['score'])

        logger.info(f"策略评估结果:")
        for candidate in candidates:
            logger.info(f"  {candidate['name']}: 评分{candidate['score']:.2f}, 内存{candidate['memory_mb']:.1f}MB")

        return (
            best_candidate['strategy'],
            best_candidate['memory_mb'] * 1024 * 1024,  # 转换回字节
            best_candidate['reason']
        )

    def _calculate_hash_strategy_score(self, total_records: int, memory_mb: float, size_ratio: float) -> float:
        """
        计算哈希策略的评分

        Args:
            total_records: 总记录数
            memory_mb: 内存使用量(MB)
            size_ratio: 数据源大小比例

        Returns:
            策略评分（越高越好）
        """
        score = 100.0  # 基础分

        # 数据量因子：小数据集加分
        if total_records <= self.small_data_threshold:
            score += 50  # 小数据集大幅加分
        elif total_records <= self.small_data_threshold * 2:
            score += 20  # 中等数据集适度加分
        else:
            score -= 30  # 大数据集减分

        # 内存因子：内存使用越少越好
        if memory_mb <= self.memory_limit_mb * 0.5:
            score += 20  # 内存使用少于50%限制，加分
        elif memory_mb <= self.memory_limit_mb * 0.8:
            score += 10  # 内存使用少于80%限制，小幅加分
        else:
            score -= 20  # 内存使用接近限制，减分

        # 数据源平衡因子：数据源越平衡，哈希策略越有优势
        if size_ratio <= 2.0:
            score += 15  # 数据源平衡，加分
        elif size_ratio <= 5.0:
            score += 5   # 数据源较平衡，小幅加分
        else:
            score -= 10  # 数据源不平衡，减分

        return score

    def _calculate_merge_strategy_score(self, total_records: int, memory_mb: float, size_ratio: float) -> float:
        """
        计算归并策略的评分

        Args:
            total_records: 总记录数
            memory_mb: 内存使用量(MB)
            size_ratio: 数据源大小比例

        Returns:
            策略评分（越高越好）
        """
        score = 80.0  # 基础分（略低于哈希策略）

        # 数据量因子：大数据集加分
        if total_records >= self.small_data_threshold * 2:
            score += 40  # 大数据集大幅加分
        elif total_records >= self.small_data_threshold:
            score += 20  # 中等数据集适度加分
        else:
            score -= 10  # 小数据集减分

        # 内存因子：归并策略内存使用稳定
        score += 30  # 内存使用稳定，固定加分

        # 数据源不平衡因子：归并策略对不平衡数据源更友好
        if size_ratio >= 5.0:
            score += 20  # 数据源很不平衡，大幅加分
        elif size_ratio >= 2.0:
            score += 10  # 数据源不平衡，适度加分

        return score
    
    def get_available_strategies(self) -> Dict[str, str]:
        """获取可用的策略列表"""
        return {
            'hash': 'Hash Comparison Strategy - 适用于小到中等规模数据集',
            'merge': 'Merge Comparison Strategy - 适用于大规模数据集'
        }
    
    def validate_strategy_config(self, strategy_name: str, config: Dict[str, Any]) -> bool:
        """验证策略配置是否有效"""
        try:
            if strategy_name.lower() == 'hash':
                HashComparisonStrategy(config)
            elif strategy_name.lower() == 'merge':
                MergeComparisonStrategy(config)
            else:
                return False
            return True
        except Exception as e:
            logger.error(f"策略配置验证失败: {e}")
            return False
