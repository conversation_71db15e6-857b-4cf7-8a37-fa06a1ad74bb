#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API异常定义
"""

from typing import Any, Dict, Optional


class APIException(Exception):
    """API基础异常类"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: str = "INTERNAL_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(APIException):
    """参数验证错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=400,
            error_code="VALIDATION_ERROR",
            details=details
        )


class NotFoundError(APIException):
    """资源不存在错误"""
    
    def __init__(self, resource: str, identifier: str):
        super().__init__(
            message=f"{resource} '{identifier}' 不存在",
            status_code=404,
            error_code="NOT_FOUND",
            details={"resource": resource, "identifier": identifier}
        )


class ConflictError(APIException):
    """资源冲突错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=409,
            error_code="CONFLICT",
            details=details
        )


class UnauthorizedError(APIException):
    """未授权错误"""
    
    def __init__(self, message: str = "未授权访问"):
        super().__init__(
            message=message,
            status_code=401,
            error_code="UNAUTHORIZED"
        )


class ForbiddenError(APIException):
    """禁止访问错误"""
    
    def __init__(self, message: str = "禁止访问"):
        super().__init__(
            message=message,
            status_code=403,
            error_code="FORBIDDEN"
        )


class RateLimitError(APIException):
    """请求限流错误"""
    
    def __init__(self, message: str = "请求过于频繁，请稍后重试"):
        super().__init__(
            message=message,
            status_code=429,
            error_code="RATE_LIMIT_EXCEEDED"
        )


class TaskError(APIException):
    """任务相关错误"""
    
    def __init__(self, message: str, task_id: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=400,
            error_code="TASK_ERROR",
            details={"task_id": task_id} if task_id else {}
        )


class TaskNotFoundError(NotFoundError):
    """任务不存在错误"""
    
    def __init__(self, task_id: str):
        super().__init__("任务", task_id)


class TaskStateError(APIException):
    """任务状态错误"""
    
    def __init__(self, task_id: str, current_state: str, required_state: str):
        super().__init__(
            message=f"任务 {task_id} 当前状态为 {current_state}，无法执行需要 {required_state} 状态的操作",
            status_code=400,
            error_code="INVALID_TASK_STATE",
            details={
                "task_id": task_id,
                "current_state": current_state,
                "required_state": required_state
            }
        )


class DatabaseError(APIException):
    """数据库错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"数据库操作失败: {message}",
            status_code=500,
            error_code="DATABASE_ERROR",
            details=details
        )


class ConfigurationError(APIException):
    """配置错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"配置错误: {message}",
            status_code=400,
            error_code="CONFIGURATION_ERROR",
            details=details
        )


class ConnectionError(APIException):
    """连接错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"连接失败: {message}",
            status_code=500,
            error_code="CONNECTION_ERROR",
            details=details
        )


class ServiceUnavailableError(APIException):
    """服务不可用错误"""
    
    def __init__(self, message: str = "服务暂时不可用"):
        super().__init__(
            message=message,
            status_code=503,
            error_code="SERVICE_UNAVAILABLE"
        )
