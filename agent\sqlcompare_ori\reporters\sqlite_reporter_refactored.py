#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite报告器 - 高性能优化版
结合单一职责原则和高性能优化技术
专注于比对结果报告功能，同时保持企业级性能
"""

import os
import time
import sqlite3
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple


# 使用统一的数据模型
from models.sqlalchemy_models import DifferenceStatus
from reporters.base_reporter import BaseReporter
from core.models import DiffResult

# 配置日志
logger = logging.getLogger(__name__)





class SqliteReporter(BaseReporter):
    """
    SQLite报告器

    职责：
    1. 比对结果的存储和查询
    2. 差异记录的管理
    3. 报告数据的导出

    性能优化策略：
    1. 直接连接管理（避免上下文管理器开销）
    2. 原生批量处理（减少抽象层开销）
    3. 延迟事务提交（最大化写入性能）
    4. 内存优化设置（PRAGMA优化）
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化超高性能SQLite报告器

        Args:
            config: 配置字典，包含：
                - db_path: 数据库文件路径
                - task_id: 任务ID（可选）
                - table_name: 结果表名（可选）
                - batch_size: 批处理大小（可选，默认50000）
                - high_performance_mode: 高性能模式（可选）
                - commit_interval: 提交间隔（可选，默认100000条记录）
        """
        super().__init__(config)

        # 报告器专用配置
        self.db_path = config.get('db_path', 'diff_results.db')
        self.task_id = config.get('task_id') or f"task_{int(time.time())}"
        self.table_name = config.get('table_name', 'comparison_results')
        self.comparison_table = config.get('comparison_table', 'unknown')

        # 完全匹配原版本的配置
        self.append_mode = config.get('append_mode', False)
        self.batch_size = config.get('batch_size', 50000)
        self.high_performance_mode = config.get('high_performance_mode', True)
        self.silent_mode = config.get('silent_mode', True)
        self.commit_interval = config.get('commit_interval', 100000)  # 减少提交频率

        # 直接批量处理（避免抽象层开销）
        self.batch_buffer = []
        self.conn = None
        self.cursor = None
        self.total_records = 0
        self._lock = threading.Lock()

        # 性能监控
        self.last_commit_time = 0
        self._deferred_indexes = []
        self._transaction_active = False  # 跟踪事务状态

        # 任务管理功能（匹配原版本，但可以禁用）
        self.current_task_id = config.get('task_id')
        self.enable_task_management = config.get('enable_task_management', False)  # 默认禁用以提高性能
    
    def open(self):
        """
        打开报告目标 - 采用原版本的高性能连接方式
        """
        try:
            # 完全匹配原版本的文件处理逻辑
            if os.path.exists(self.db_path) and not self.append_mode:
                try:
                    temp_conn = sqlite3.connect(self.db_path)
                    temp_conn.close()
                    os.remove(self.db_path)
                except (PermissionError, OSError) as e:
                    timestamp = int(time.time())
                    base_name, ext = os.path.splitext(self.db_path)
                    self.db_path = f"{base_name}_{timestamp}{ext}"
            elif os.path.exists(self.db_path) and self.append_mode:
                try:
                    temp_conn = sqlite3.connect(self.db_path)
                    temp_cursor = temp_conn.cursor()
                    temp_cursor.execute(f"DELETE FROM {self.table_name} WHERE table_name = ?", (self.comparison_table,))
                    temp_conn.commit()
                    temp_conn.close()
                except Exception as e:
                    timestamp = int(time.time())
                    base_name, ext = os.path.splitext(self.db_path)
                    self.db_path = f"{base_name}_{timestamp}{ext}"

            # 直接创建连接（避免连接管理器开销）
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)

            # 高性能PRAGMA设置（采用原版本的优化设置）
            if self.high_performance_mode:
                self.conn.execute("PRAGMA journal_mode=MEMORY")     # 内存模式，最快
                self.conn.execute("PRAGMA synchronous=OFF")         # 关闭同步，最大性能
                self.conn.execute("PRAGMA cache_size=100000")       # 大幅增大缓存
                self.conn.execute("PRAGMA temp_store=MEMORY")       # 临时数据存储在内存
                self.conn.execute("PRAGMA locking_mode=EXCLUSIVE")  # 独占锁模式
                self.conn.execute("PRAGMA count_changes=OFF")       # 关闭计数
                self.conn.execute("PRAGMA auto_vacuum=NONE")        # 关闭自动清理
            else:
                self.conn.execute("PRAGMA journal_mode=WAL")
                self.conn.execute("PRAGMA synchronous=NORMAL")
                self.conn.execute("PRAGMA cache_size=50000")
                self.conn.execute("PRAGMA temp_store=MEMORY")

            self.cursor = self.conn.cursor()

            # 创建表结构
            self._create_tables_direct()

            if not self.silent_mode:
                print(f"超高性能SQLite报告器已初始化: {self.db_path}")
                print(f"批处理大小: {self.batch_size}")
                print(f"高性能模式: {'启用' if self.high_performance_mode else '禁用'}")

        except sqlite3.Error as e:
            logger.error(f"SQLite数据库初始化失败: {e}")
            print(f"SQLite数据库初始化失败: {e}")
            raise

    def _create_tables_direct(self):
        """直接创建数据库表结构（完全匹配原版本的高性能结构）"""
        # 创建比对结果表（完全匹配原版本结构，最大化性能）
        create_table_sql = f'''
            CREATE TABLE IF NOT EXISTS {self.table_name} (
                id INTEGER PRIMARY KEY,
                table_name TEXT NOT NULL,
                record_key TEXT NOT NULL,
                status TEXT NOT NULL,
                field_name TEXT,
                value_a TEXT,
                value_b TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        '''
        self.cursor.execute(create_table_sql)

        # 检查并添加缺失的列（向后兼容）
        self._ensure_table_schema_direct()

        # 创建比对统计表（保持兼容性）
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS comparison_statistics (
                task_id TEXT PRIMARY KEY,
                table_name TEXT,
                total_source_records INTEGER DEFAULT 0,
                total_target_records INTEGER DEFAULT 0,
                total_compared_records INTEGER DEFAULT 0,
                identical_records INTEGER DEFAULT 0,
                different_records INTEGER DEFAULT 0,
                source_only_records INTEGER DEFAULT 0,
                target_only_records INTEGER DEFAULT 0,
                field_differences TEXT,
                execution_time_seconds REAL DEFAULT 0.0,
                consistency_percentage REAL DEFAULT 0.0,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        ''')

        # 在高性能模式下延迟创建索引（匹配原版本索引）
        if not self.high_performance_mode:
            self._create_indexes_immediate_direct()
        else:
            # 记录需要延迟创建的索引（匹配原版本）
            self._deferred_indexes = [
                f"CREATE INDEX IF NOT EXISTS idx_{self.table_name}_table_status ON {self.table_name}(table_name, status)",
                f"CREATE INDEX IF NOT EXISTS idx_{self.table_name}_table_key ON {self.table_name}(table_name, record_key)",
                f"CREATE INDEX IF NOT EXISTS idx_{self.table_name}_created_at ON {self.table_name}(created_at)"
            ]

        self.conn.commit()

    def _ensure_table_schema_direct(self):
        """直接确保表结构包含所有必需的列（匹配原版本结构）"""
        try:
            # 获取当前表结构
            self.cursor.execute(f"PRAGMA table_info({self.table_name})")
            columns = {row[1]: row[2] for row in self.cursor.fetchall()}

            # 检查并添加缺失的列（匹配原版本结构）
            required_columns = {
                'table_name': 'TEXT NOT NULL',
                'record_key': 'TEXT NOT NULL',
                'status': 'TEXT NOT NULL',
                'field_name': 'TEXT',
                'value_a': 'TEXT',
                'value_b': 'TEXT'
            }

            for column_name, column_type in required_columns.items():
                if column_name not in columns:
                    # 为缺失的列设置默认值
                    if 'NOT NULL' in column_type:
                        default_value = 'DEFAULT ""'
                    else:
                        default_value = ''

                    alter_sql = f'ALTER TABLE {self.table_name} ADD COLUMN {column_name} {column_type} {default_value}'
                    self.cursor.execute(alter_sql)
                    logger.info(f"添加缺失列: {column_name}")

        except sqlite3.Error as e:
            logger.warning(f"表结构检查失败: {e}")
            # 继续执行，不中断流程

    def _create_indexes_immediate_direct(self):
        """直接创建数据库索引（带错误处理）"""
        indexes = [
            f"CREATE INDEX IF NOT EXISTS idx_{self.table_name}_table_status ON {self.table_name}(table_name, status)",
            f"CREATE INDEX IF NOT EXISTS idx_{self.table_name}_table_key ON {self.table_name}(table_name, record_key)",
            f"CREATE INDEX IF NOT EXISTS idx_{self.table_name}_created_at ON {self.table_name}(created_at)"
        ]

        for index_sql in indexes:
            try:
                self.cursor.execute(index_sql)
                logger.debug(f"索引创建成功: {index_sql}")
            except sqlite3.Error as e:
                logger.warning(f"索引创建失败: {index_sql}, 错误: {e}")
                # 继续创建其他索引，不中断流程

    def _create_deferred_indexes_direct(self):
        """直接创建延迟的索引（在数据插入完成后）"""
        if not self._deferred_indexes:
            return

        start_time = time.time()
        for index_sql in self._deferred_indexes:
            try:
                self.cursor.execute(index_sql)
                logger.debug(f"延迟索引创建成功: {index_sql}")
            except sqlite3.Error as e:
                logger.warning(f"延迟索引创建失败: {index_sql}, 错误: {e}")
                # 继续创建其他索引，不中断流程
        self.conn.commit()

        index_time = time.time() - start_time
        if not self.silent_mode:
            print(f"延迟索引创建完成，耗时: {index_time:.2f}秒")
        logger.info(f"延迟索引创建完成，耗时: {index_time:.2f}秒")

    def close(self):
        """
        关闭报告目标 - 采用原版本的高性能关闭方式
        """
        try:
            # 提交剩余的批量数据
            if self.batch_buffer:
                self._flush_batch_direct()

            if self.conn:
                # 最终提交所有未提交的数据
                if self._transaction_active:
                    self.conn.commit()
                    self._transaction_active = False

                # 在高性能模式下创建延迟的索引
                if self.high_performance_mode and self._deferred_indexes:
                    if not self.silent_mode:
                        print("正在创建索引以优化查询性能...")

                    start_time = time.time()
                    self._create_deferred_indexes_direct()

                    if not self.silent_mode:
                        index_time = time.time() - start_time
                        print(f"索引创建完成，耗时: {index_time:.2f}秒")

                self.conn.close()
                self.conn = None
                self.cursor = None

            if not self.silent_mode:
                print(f"超高性能SQLite报告器已关闭，总计处理 {self.total_records} 条记录")

            logger.info(f"SQLite报告器关闭，处理记录: {self.total_records}")

        except Exception as e:
            logger.error(f"关闭SQLite报告器时出错: {e}")
            print(f"关闭SQLite报告器时出错: {e}")

    def report_diff(self, diff_result: DiffResult):
        """
        记录一条差异 - 完全匹配原版本的高性能处理
        """
        if not self.cursor:
            raise ConnectionError("数据库未连接或游标不可用。")

        with self._lock:
            # 解析差异数据，支持字段级别的差异（完全匹配原版本格式）
            if hasattr(diff_result, 'field_diffs') and diff_result.field_diffs:
                # 字段级别差异
                for field_name, (value_a, value_b) in diff_result.field_diffs.items():
                    self.batch_buffer.append((
                        self.comparison_table,
                        diff_result.key,
                        'FIELD_DIFF',
                        field_name,
                        str(value_a) if value_a is not None else None,
                        str(value_b) if value_b is not None else None
                    ))
            else:
                # 记录级别差异
                value_a = getattr(diff_result, 'value_a', None)
                value_b = getattr(diff_result, 'value_b', None)
                self.batch_buffer.append((
                    self.comparison_table,
                    diff_result.key,
                    diff_result.status,
                    None,  # field_name
                    str(value_a) if value_a is not None else None,
                    str(value_b) if value_b is not None else None
                ))

            self.total_records += 1

            # 批量提交 - 完全匹配原版本策略
            if len(self.batch_buffer) >= self.batch_size:
                self._flush_batch_direct()

            # 定期提交以避免内存过度使用
            elif self.total_records % self.commit_interval == 0:
                current_time = time.time()
                if current_time - self.last_commit_time > 5:  # 至少5秒提交一次
                    self._flush_batch_direct()
                    self.last_commit_time = current_time

    def _flush_batch_direct(self):
        """
        直接批量数据刷新 - 完全匹配原版本的高性能策略
        """
        if not self.batch_buffer:
            return

        try:
            batch_count = len(self.batch_buffer)

            if self.high_performance_mode:
                # 高性能模式：智能事务管理
                if not self._transaction_active:
                    self.conn.execute("BEGIN IMMEDIATE")
                    self._transaction_active = True

                insert_sql = f"""
                INSERT INTO {self.table_name} (table_name, record_key, status, field_name, value_a, value_b)
                VALUES (?, ?, ?, ?, ?, ?);
                """
                self.cursor.executemany(insert_sql, self.batch_buffer)

                # 完全匹配原版本的提交策略
                if batch_count >= self.batch_size or self.total_records % (self.batch_size * 5) == 0:
                    self.conn.commit()
                    self._transaction_active = False
                # 否则不提交，继续累积事务
            else:
                # 标准模式
                insert_sql = f"""
                INSERT INTO {self.table_name} (table_name, record_key, status, field_name, value_a, value_b)
                VALUES (?, ?, ?, ?, ?, ?);
                """
                self.cursor.executemany(insert_sql, self.batch_buffer)
                self.conn.commit()

            self.batch_buffer.clear()

            # 只在非静默模式下输出批量提交信息
            if not self.silent_mode and batch_count >= 10000:
                print(f"SQLite批量提交: {batch_count} 条记录，总计: {self.total_records}")

        except sqlite3.Error as e:
            logger.error(f"批量插入失败: {e}")
            print(f"向 SQLite 批量插入数据失败: {e}")
            raise

    def report_difference(
        self,
        table_name: str,
        record_key: str,
        difference_type: DifferenceStatus,
        field_name: str = None,
        source_value: Any = None,
        target_value: Any = None
    ):
        """
        报告差异记录（优化版本，使用批处理）

        Args:
            table_name: 表名
            record_key: 记录主键
            difference_type: 差异类型
            field_name: 字段名（字段级差异时使用）
            source_value: 源值
            target_value: 目标值
        """
        current_time = datetime.now().isoformat()

        record = (
            self.task_id, table_name, record_key, difference_type.value,
            field_name,
            str(source_value) if source_value is not None else None,
            str(target_value) if target_value is not None else None,
            'new', None, current_time, current_time
        )

        # 使用批处理器，避免频繁的数据库连接
        should_flush = self.batch_processor.add_record(record)
        if should_flush or self.batch_processor.should_commit():
            self._flush_current_batch()
    
    def report_batch_differences(self, differences: List[Dict[str, Any]]):
        """
        批量报告差异记录（高性能优化版本）

        Args:
            differences: 差异记录列表
        """
        if not differences:
            return

        current_time = datetime.now().isoformat()

        # 准备批量数据
        batch_data = []
        for diff in differences:
            record = (
                self.task_id,
                diff.get('table_name', ''),
                diff.get('record_key', ''),
                diff.get('difference_type', DifferenceStatus.DIFFERENT).value,
                diff.get('field_name'),
                str(diff.get('source_value')) if diff.get('source_value') is not None else None,
                str(diff.get('target_value')) if diff.get('target_value') is not None else None,
                'new',
                None,
                current_time,
                current_time
            )
            batch_data.append(record)

        # 直接刷新批量数据，避免通过批处理器的额外开销
        self._flush_batch_data(batch_data)
        self.total_records += len(differences)
    
    def get_comparison_results(
        self,
        table_name: str = None,
        difference_type: DifferenceStatus = None,
        status: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取比对结果（使用连接管理器的优化版本）

        Args:
            table_name: 表名过滤
            difference_type: 差异类型过滤
            status: 状态过滤
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            (结果列表, 总记录数)
        """
        try:
            # 直接使用连接，避免connection_manager开销
            if not self.conn:
                return [], 0

            # 构建查询条件
            conditions = []
            params = []

            if table_name:
                conditions.append('table_name = ?')
                params.append(table_name)

            if status:
                conditions.append('status = ?')
                params.append(status)

            where_clause = ' AND '.join(conditions) if conditions else '1=1'

            # 获取总记录数
            count_sql = f"SELECT COUNT(*) FROM {self.table_name} WHERE {where_clause}"
            self.cursor.execute(count_sql, params)
            total_count = self.cursor.fetchone()[0]

            # 获取分页数据（匹配原版本字段）
            data_sql = f'''
                SELECT id, record_key, status, field_name, value_a, value_b, created_at
                FROM {self.table_name}
                WHERE {where_clause}
                ORDER BY id DESC
                LIMIT ? OFFSET ?
            '''
            params.extend([limit, offset])
            self.cursor.execute(data_sql, params)

            results = []
            for row in self.cursor.fetchall():
                results.append({
                    'id': row[0],
                    'record_key': row[1],
                    'status': row[2],
                    'field_name': row[3],
                    'value_a': row[4],
                    'value_b': row[5],
                    'created_at': row[6]
                })

            return results, total_count

        except sqlite3.Error as e:
            print(f"查询比对结果失败: {e}")
            return [], 0

    def search_records(
        self,
        table_name: str,
        search_term: str,
        page: int = 1,
        page_size: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        搜索包含指定关键词的记录（从原版本迁移）

        Args:
            table_name: 表名
            search_term: 搜索关键词
            page: 页码
            page_size: 每页记录数

        Returns:
            (结果列表, 总记录数)
        """
        if not self.conn:
            return [], 0

        try:
            # 构建搜索查询
            search_pattern = f"%{search_term}%"
            where_clause = '''
                table_name = ? AND (
                    record_key LIKE ? OR
                    source_value LIKE ? OR
                    target_value LIKE ? OR
                    field_name LIKE ?
                )
            '''
            params = [table_name, search_pattern, search_pattern, search_pattern, search_pattern]

            if self.task_id:
                where_clause = f"task_id = ? AND {where_clause}"
                params.insert(0, self.task_id)

            # 获取总记录数
            count_sql = f"SELECT COUNT(*) FROM {self.table_name} WHERE {where_clause}"
            cursor = self.conn.cursor()
            cursor.execute(count_sql, params)
            total_count = cursor.fetchone()[0]

            # 获取分页数据
            offset = (page - 1) * page_size
            data_sql = f'''
                SELECT id, record_key, difference_type as status, field_name,
                       source_value as value_a, target_value as value_b, created_at
                FROM {self.table_name}
                WHERE {where_clause}
                ORDER BY id DESC
                LIMIT ? OFFSET ?
            '''
            params.extend([page_size, offset])
            cursor.execute(data_sql, params)

            results = []
            for row in cursor.fetchall():
                results.append({
                    'id': row[0],
                    'record_key': row[1],
                    'status': row[2],
                    'field_name': row[3],
                    'value_a': row[4],
                    'value_b': row[5],
                    'created_at': row[6]
                })

            return results, total_count

        except sqlite3.Error as e:
            print(f"搜索记录失败: {e}")
            return [], 0
    
    def get_comparison_summary(self, table_name: str = None) -> Optional[Dict[str, Any]]:
        """
        获取比对摘要统计

        Args:
            table_name: 表名过滤

        Returns:
            比对摘要字典
        """
        if not self.conn:
            return None

        try:
            cursor = self.conn.cursor()

            # 构建查询条件
            conditions = []
            params = []

            if self.task_id:
                conditions.append('task_id = ?')
                params.append(self.task_id)

            if table_name:
                conditions.append('table_name = ?')
                params.append(table_name)

            where_condition = ' AND '.join(conditions) if conditions else '1=1'

            # 获取各类型差异统计
            cursor.execute(f'''
                SELECT
                    difference_type,
                    COUNT(*) as count
                FROM {self.table_name}
                WHERE {where_condition}
                GROUP BY difference_type
            ''', params)

            diff_stats = {row[0]: row[1] for row in cursor.fetchall()}

            # 获取字段级差异统计
            cursor.execute(f'''
                SELECT
                    field_name,
                    COUNT(*) as count
                FROM {self.table_name}
                WHERE {where_condition} AND field_name IS NOT NULL
                GROUP BY field_name
            ''', params)

            field_stats = {row[0]: row[1] for row in cursor.fetchall()}

            # 计算总计
            total_differences = sum(diff_stats.values())
            different_records = diff_stats.get(DifferenceStatus.DIFFERENT.value, 0)
            source_only = diff_stats.get(DifferenceStatus.SOURCE_ONLY.value, 0)
            target_only = diff_stats.get(DifferenceStatus.TARGET_ONLY.value, 0)
            identical_records = diff_stats.get(DifferenceStatus.IDENTICAL.value, 0)

            return {
                'task_id': self.task_id,
                'table_name': table_name or 'ALL_TABLES',
                'total_differences': total_differences,
                'different_records': different_records,
                'source_only_records': source_only,
                'target_only_records': target_only,
                'identical_records': identical_records,
                'field_differences': field_stats
            }

        except sqlite3.Error as e:
            print(f"获取比对摘要失败: {e}")
            return None

    def get_table_summary(self, table_name: str) -> Dict[str, int]:
        """
        获取表的差异统计摘要（兼容原版本接口）

        Args:
            table_name: 表名

        Returns:
            差异统计字典
        """
        if not self.conn:
            return {}

        try:
            cursor = self.conn.cursor()

            # 构建查询条件
            conditions = ['table_name = ?']
            params = [table_name]

            if self.task_id:
                conditions.append('task_id = ?')
                params.append(self.task_id)

            where_condition = ' AND '.join(conditions)

            summary_sql = f'''
                SELECT difference_type as status, COUNT(*) as count
                FROM {self.table_name}
                WHERE {where_condition}
                GROUP BY difference_type
            '''
            cursor.execute(summary_sql, params)

            summary = {}
            for row in cursor.fetchall():
                summary[row[0]] = row[1]

            return summary

        except sqlite3.Error as e:
            print(f"获取表摘要失败: {e}")
            return {}
    
    def update_record_status(
        self,
        record_id: int,
        status: str,
        comment: str = None
    ) -> bool:
        """
        更新差异记录状态（使用连接管理器优化）

        Args:
            record_id: 记录ID
            status: 新状态
            comment: 备注

        Returns:
            是否更新成功
        """
        current_time = datetime.now().isoformat()

        try:
            if not self.conn:
                return False

            self.cursor.execute(f'''
                UPDATE {self.table_name}
                SET status = ?, comment = ?
                WHERE id = ?
            ''', (status, comment, record_id))

            success = self.cursor.rowcount > 0
            self.conn.commit()
            return success

        except sqlite3.Error as e:
            return False
    
    def save_comparison_statistics(self, statistics: Dict[str, Any]):
        """
        保存比对统计信息（使用连接管理器优化）

        Args:
            statistics: 统计信息字典
        """
        current_time = datetime.now().isoformat()

        try:
            if not self.conn:
                return

            self.cursor.execute('''
                INSERT OR REPLACE INTO comparison_statistics (
                    task_id, table_name, total_source_records, total_target_records,
                    total_compared_records, identical_records, different_records,
                    source_only_records, target_only_records, field_differences,
                    execution_time_seconds, consistency_percentage,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.task_id,
                statistics.get('table_name', 'ALL_TABLES'),
                statistics.get('total_source_records', 0),
                statistics.get('total_target_records', 0),
                statistics.get('total_compared_records', 0),
                statistics.get('identical_records', 0),
                statistics.get('different_records', 0),
                statistics.get('source_only_records', 0),
                statistics.get('target_only_records', 0),
                str(statistics.get('field_differences', {})),
                statistics.get('execution_time_seconds', 0.0),
                statistics.get('consistency_percentage', 0.0),
                current_time,
                current_time
            ))
            self.conn.commit()

        except sqlite3.Error as e:
            print(f"保存统计信息失败: {e}")
    
    def export_results_to_csv(self, output_path: str, table_name: str = None) -> bool:
        """
        导出结果到CSV文件
        
        Args:
            output_path: 输出文件路径
            table_name: 表名过滤
            
        Returns:
            是否导出成功
        """
        try:
            import csv
            
            results = self.get_comparison_results(table_name=table_name, limit=1000000)
            
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                if not results:
                    return True
                
                fieldnames = results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    writer.writerow(result)
            
            return True
            
        except Exception as e:
            print(f"导出CSV失败: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        return {
            'total_records': self.total_records,
            'batch_buffer_size': len(self.batch_buffer),
            'batch_size': self.batch_size,
            'high_performance_mode': self.high_performance_mode,
            'db_path': self.db_path,
            'table_name': self.table_name,
            'last_commit_time': self.last_commit_time
        }

    def force_commit(self):
        """
        强制提交当前批处理数据
        用于确保数据及时写入数据库
        """
        if self.batch_buffer:
            self._flush_batch_direct()
        if self.conn and self._transaction_active:
            self.conn.commit()
            self._transaction_active = False
            logger.info(f"强制提交完成")

    # ==================== 任务管理API兼容性方法 ====================

    def create_comparison_task(self, name: str, description: str = None, **kwargs) -> Optional[str]:
        """
        创建新的比对任务（兼容性方法）

        Args:
            name: 任务名称
            description: 任务描述
            **kwargs: 其他任务参数

        Returns:
            任务ID（如果启用任务管理）或None
        """
        if not self.enable_task_management:
            logger.debug("任务管理已禁用，跳过任务创建")
            return None

        # 简单的任务ID生成（实际实现中应该有完整的任务管理）
        task_id = f"task_{int(time.time())}"
        logger.info(f"创建比对任务: {name} (ID: {task_id})")
        return task_id

    def start_task(self, task_id: str = None):
        """
        开始执行任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        if not self.enable_task_management:
            logger.debug("任务管理已禁用，跳过任务启动")
            return

        task_id = task_id or self.current_task_id
        if task_id:
            logger.info(f"开始执行任务: {task_id}")
        else:
            logger.warning("未指定任务ID，无法启动任务")

    def complete_task(self, task_id: str = None, **summary):
        """
        完成任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            **summary: 任务摘要信息
        """
        if not self.enable_task_management:
            logger.debug("任务管理已禁用，跳过任务完成")
            return

        task_id = task_id or self.current_task_id
        if task_id:
            logger.info(f"完成任务: {task_id}, 摘要: {summary}")
        else:
            logger.warning("未指定任务ID，无法完成任务")

    def fail_task(self, task_id: str = None, error_msg: str = None):
        """
        标记任务失败（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            error_msg: 错误信息
        """
        if not self.enable_task_management:
            logger.debug("任务管理已禁用，跳过任务失败标记")
            return

        task_id = task_id or self.current_task_id
        if task_id:
            logger.error(f"任务失败: {task_id}, 错误: {error_msg}")
        else:
            logger.warning("未指定任务ID，无法标记任务失败")

    def update_task_progress(self, step_name: str, progress: float,
                           processed: int = 0, total: int = 0, message: str = None, task_id: str = None):
        """
        更新任务进度（兼容性方法）

        Args:
            step_name: 步骤名称
            progress: 进度百分比
            processed: 已处理记录数
            total: 总记录数
            message: 进度消息
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        if not self.enable_task_management:
            logger.debug("任务管理已禁用，跳过进度更新")
            return

        task_id = task_id or self.current_task_id
        if task_id:
            logger.debug(f"任务进度更新: {task_id} - {step_name} - {progress}% ({processed}/{total})")
        else:
            logger.warning("未指定任务ID，无法更新任务进度")

    def get_task_info(self, task_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取任务信息（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID

        Returns:
            任务信息字典或None
        """
        if not self.enable_task_management:
            logger.debug("任务管理已禁用，返回None")
            return None

        task_id = task_id or self.current_task_id
        if task_id:
            # 返回基本的任务信息（实际实现中应该从数据库查询）
            return {
                'task_id': task_id,
                'status': 'running',
                'total_records': self.total_records,
                'created_at': datetime.now().isoformat()
            }
        return None
