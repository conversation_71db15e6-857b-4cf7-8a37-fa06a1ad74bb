#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL报告器 - 基于SQLAlchemy服务
将数据比对结果存储到PostgreSQL数据库中
"""
import os
import time
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from io import StringIO

# 使用统一的数据模型和服务
from models.sqlalchemy_models import DifferenceStatus
from services.sqlalchemy_service import SQLAlchemyComparisonService
from reporters.base_reporter import BaseReporter
from core.models import DiffResult

# 配置日志
logger = logging.getLogger(__name__)

import psycopg2
import psycopg2.extras
from psycopg2.pool import ThreadedConnectionPool

try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False
    logger.warning("asyncpg未安装，异步PostgreSQL功能不可用")


class PostgreSQLBatchWriter:
    """
    PostgreSQL优化的批处理写入器
    支持COPY命令和批量INSERT优化
    """

    def __init__(self, service: SQLAlchemyComparisonService, config: Dict[str, Any]):
        self.service = service
        self.batch_size = config.get('batch_size', 50000)
        self.use_copy = config.get('use_copy', True)  # 是否使用COPY命令
        self.high_performance_mode = config.get('high_performance_mode', True)
        self.buffer = []
        self._lock = threading.Lock()
        self.total_records = 0
        self.last_commit_time = time.time()
        self.commit_interval = config.get('commit_interval', 100000)

        # PostgreSQL连接池配置
        self.pg_config = config.get('postgres_config', {})
        self.connection_pool = None
        
        if self.use_copy:
            self._init_connection_pool()

        logger.info(f"PostgreSQLBatchWriter初始化: batch_size={self.batch_size}, "
                   f"use_copy={self.use_copy}, high_performance={self.high_performance_mode}")

    def _init_connection_pool(self):
        """初始化PostgreSQL连接池"""
        try:
            if not self.pg_config:
                logger.warning("PostgreSQL配置为空，跳过连接池初始化")
                return

            self.connection_pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=5,
                host=self.pg_config.get('host', 'localhost'),
                port=self.pg_config.get('port', 5432),
                database=self.pg_config.get('database', 'postgres'),
                user=self.pg_config.get('username', 'postgres'),
                password=self.pg_config.get('password', ''),
                application_name='sqlcompare_reporter'
            )
            logger.info("PostgreSQL连接池初始化成功")
        except Exception as e:
            logger.error(f"PostgreSQL连接池初始化失败: {e}")
            self.connection_pool = None
            self.use_copy = False

    def add_record(self, record: Dict[str, Any]):
        """添加记录到批处理缓冲区"""
        with self._lock:
            self.buffer.append(record)
            self.total_records += 1

            # 检查是否需要刷新批处理
            if len(self.buffer) >= self.batch_size:
                self._flush_batch()
            elif self.total_records % self.commit_interval == 0:
                current_time = time.time()
                if current_time - self.last_commit_time > 5:  # 至少5秒提交一次
                    self._flush_batch()
                    self.last_commit_time = current_time

    def _flush_batch(self):
        """高性能批量刷新到PostgreSQL"""
        if not self.buffer:
            return

        try:
            batch_count = len(self.buffer)
            start_time = time.time()

            if self.use_copy and self.connection_pool:
                # 使用PostgreSQL COPY命令进行高性能批量插入
                self._flush_with_copy()
            else:
                # 使用SQLAlchemy服务的批量插入
                self._flush_with_sqlalchemy()

            self.buffer.clear()

            elapsed_time = time.time() - start_time
            logger.debug(f"PostgreSQL批量提交完成: {batch_count} 条记录，耗时: {elapsed_time:.3f}秒")

        except Exception as e:
            logger.error(f"PostgreSQL批量提交失败: {e}")
            # 清空缓冲区避免重复提交
            self.buffer.clear()
            raise

    def _flush_with_copy(self):
        """使用PostgreSQL COPY命令进行批量插入"""
        conn = None
        try:
            conn = self.connection_pool.getconn()
            cursor = conn.cursor()

            # 准备COPY数据
            copy_data = StringIO()
            for record in self.buffer:
                # 格式化数据为COPY格式（制表符分隔）
                values = [
                    record.get('task_id', ''),
                    record.get('table_name', ''),
                    record.get('record_key', ''),
                    record.get('status', ''),
                    record.get('field_name', ''),
                    record.get('source_value', ''),
                    record.get('target_value', ''),
                    datetime.now().isoformat()
                ]
                # 转义特殊字符并连接
                escaped_values = [str(v).replace('\t', '\\t').replace('\n', '\\n').replace('\r', '\\r') for v in values]
                copy_data.write('\t'.join(escaped_values) + '\n')

            copy_data.seek(0)

            # 执行COPY命令
            cursor.copy_from(
                copy_data,
                'comparison_results',
                columns=('task_id', 'table_name', 'record_key', 'status', 'field_name', 'source_value', 'target_value', 'created_at'),
                sep='\t'
            )
            conn.commit()

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"COPY命令执行失败: {e}")
            # 回退到SQLAlchemy方式
            self._flush_with_sqlalchemy()
        finally:
            if conn:
                self.connection_pool.putconn(conn)

    def _flush_with_sqlalchemy(self):
        """使用SQLAlchemy服务的批量插入"""
        with self.service.get_db_session() as session:
            from models.sqlalchemy_models import ComparisonResult

            # 转换为SQLAlchemy模型对象
            result_objects = []
            for record in self.buffer:
                result_obj = ComparisonResult(
                    task_id=record.get('task_id'),
                    table_name=record.get('table_name'),
                    record_key=record.get('record_key'),
                    status=record.get('status'),
                    field_name=record.get('field_name'),
                    source_value=record.get('source_value'),
                    target_value=record.get('target_value')
                )
                result_objects.append(result_obj)

            # 批量插入
            session.bulk_save_objects(result_objects)

    def force_flush(self):
        """强制刷新所有缓冲的记录"""
        with self._lock:
            self._flush_batch()

    def get_stats(self) -> Dict[str, Any]:
        """获取批处理统计信息"""
        return {
            'total_records': self.total_records,
            'buffer_size': len(self.buffer),
            'batch_size': self.batch_size,
            'use_copy': self.use_copy,
            'high_performance_mode': self.high_performance_mode,
            'connection_pool_available': self.connection_pool is not None
        }

    def __del__(self):
        """析构函数，清理连接池"""
        if self.connection_pool:
            try:
                self.connection_pool.closeall()
            except Exception as e:
                logger.error(f"关闭PostgreSQL连接池失败: {e}")


class PostgresReporter(BaseReporter):
    """PostgreSQL报告器"""

    def __init__(self, config: Dict[str, Any]):
        """初始化PostgreSQL报告器"""
        super().__init__(config)

        # 基础配置
        self.task_id = config.get('task_id') or f"task_{int(time.time())}"
        self.table_name = config.get('table_name', 'comparison_results')
        self.comparison_table = config.get('comparison_table', 'unknown')

        # PostgreSQL连接配置
        self.pg_config = {
            'host': config.get('host', 'localhost'),
            'port': config.get('port', 5432),
            'database': config.get('database', 'postgres'),
            'username': config.get('username', 'postgres'),
            'password': config.get('password', ''),
            'schema': config.get('schema', 'public')
        }

        # 性能配置
        self.append_mode = config.get('append_mode', False)
        self.batch_size = config.get('batch_size', 50000)
        self.use_copy = config.get('use_copy', True)
        self.high_performance_mode = config.get('high_performance_mode', True)
        self.silent_mode = config.get('silent_mode', True)
        self.commit_interval = config.get('commit_interval', 100000)

        # 构建PostgreSQL数据库URL
        database_url = (f"postgresql://{self.pg_config['username']}:{self.pg_config['password']}"
                       f"@{self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}")

        # 初始化SQLAlchemy服务（使用单例模式）
        self.service = SQLAlchemyComparisonService.get_instance(database_url)

        # 将PostgreSQL配置传递给批处理器
        batch_config = config.copy()
        batch_config['postgres_config'] = self.pg_config

        # 初始化高性能批处理器
        self.batch_writer = PostgreSQLBatchWriter(self.service, batch_config)

        # 性能统计
        self.stats = {
            'total_inserts': 0,
            'batch_commits': 0,
            'total_time': 0,
            'avg_batch_time': 0
        }

        # 向后兼容的属性
        self.total_records = 0
        self._lock = threading.Lock()

        logger.info(f"PostgresReporter初始化完成: {database_url}")
        logger.info(f"配置 - 批处理大小: {self.batch_size}, 使用COPY: {self.use_copy}, 高性能模式: {self.high_performance_mode}")

    def _test_connection(self) -> bool:
        """测试PostgreSQL连接"""
        try:
            with self.service.get_db_session() as session:
                session.execute("SELECT 1")
            logger.info("PostgreSQL连接测试成功")
            return True
        except Exception as e:
            logger.error(f"PostgreSQL连接测试失败: {e}")
            return False

    def open(self):
        """打开报告目标 - 使用SQLAlchemy服务初始化"""
        try:
            # 测试数据库连接
            if not self._test_connection():
                raise ConnectionError("无法连接到PostgreSQL数据库")

            # 处理追加模式
            if self.append_mode:
                try:
                    with self.service.get_db_session() as session:
                        from models.sqlalchemy_models import ComparisonResult
                        session.query(ComparisonResult).filter(
                            ComparisonResult.table_name == self.comparison_table
                        ).delete()
                        session.commit()
                        logger.info(f"清理表 {self.comparison_table} 的现有数据")
                except Exception as e:
                    logger.warning(f"清理现有数据失败: {e}")

            # 确保SQLAlchemy服务的表结构已初始化
            self.service._ensure_tables_initialized()

            if not self.silent_mode:
                print(f"PostgreSQL报告器已初始化")
                print(f"数据库: {self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}")
                print(f"批处理大小: {self.batch_size}")
                print(f"使用COPY命令: {'启用' if self.use_copy else '禁用'}")
                print(f"高性能模式: {'启用' if self.high_performance_mode else '禁用'}")

            logger.info(f"PostgresReporter打开完成")

        except Exception as e:
            logger.error(f"PostgreSQL报告器初始化失败: {e}")
            if not self.silent_mode:
                print(f"PostgreSQL报告器初始化失败: {e}")
            raise

    def _convert_diff_to_service_record(self, diff_result: DiffResult) -> List[Dict[str, Any]]:
        """
        将DiffResult转换为SQLAlchemy服务兼容的记录格式

        Args:
            diff_result: 差异结果对象

        Returns:
            服务兼容的记录字典列表
        """
        # 基础记录信息
        base_record = {
            'task_id': self.task_id,
            'table_name': self.comparison_table,
            'record_key': diff_result.key,
            'status': diff_result.status,
            'field_name': None,
            'source_value': None,
            'target_value': None
        }

        # 处理字段级别的差异
        if hasattr(diff_result, 'field_diffs') and diff_result.field_diffs:
            # 返回多个记录，每个字段差异一个记录
            records = []
            for field_name, (value_a, value_b) in diff_result.field_diffs.items():
                record = base_record.copy()
                record.update({
                    'status': 'FIELD_DIFF',
                    'field_name': field_name,
                    'source_value': str(value_a) if value_a is not None else None,
                    'target_value': str(value_b) if value_b is not None else None
                })
                records.append(record)
            return records
        else:
            # 记录级别差异
            base_record.update({
                'source_value': str(getattr(diff_result, 'value_a', None)) if getattr(diff_result, 'value_a', None) is not None else None,
                'target_value': str(getattr(diff_result, 'value_b', None)) if getattr(diff_result, 'value_b', None) is not None else None
            })
            return [base_record]

    def close(self):
        """关闭报告目标 - 使用SQLAlchemy服务的资源清理"""
        try:
            # 强制刷新批处理器中的剩余数据
            if hasattr(self, 'batch_writer'):
                self.batch_writer.force_flush()

            # SQLAlchemy服务会自动管理连接和事务
            # 无需手动关闭连接

            if not self.silent_mode:
                print(f"PostgreSQL报告器已关闭，总计处理 {self.total_records} 条记录")
                print(f"批处理统计: {self.stats}")

            logger.info(f"PostgresReporter关闭，处理记录: {self.total_records}")
            logger.info(f"性能统计: {self.stats}")

        except Exception as e:
            logger.error(f"关闭PostgreSQL报告器时出错: {e}")
            if not self.silent_mode:
                print(f"关闭PostgreSQL报告器时出错: {e}")

    def report_diff(self, diff_result: DiffResult):
        """记录一条差异 - 使用SQLAlchemy服务的高性能处理"""
        try:
            # 转换为服务兼容的记录格式
            records = self._convert_diff_to_service_record(diff_result)

            # 使用高性能批处理器处理记录
            for record in records:
                self.batch_writer.add_record(record)
                self.total_records += 1

            # 更新统计信息
            self.stats['total_inserts'] += len(records)

        except Exception as e:
            logger.error(f"报告差异失败: {e}")
            raise

    def get_comparison_results(
        self,
        table_name: str = None,
        difference_type: DifferenceStatus = None,
        status: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取比对结果 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名过滤
            difference_type: 差异类型过滤
            status: 状态过滤
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            (结果列表, 总记录数)
        """
        try:
            # 委托给SQLAlchemy服务进行查询
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import func

                # 构建查询
                query = session.query(ComparisonResult)

                # 添加过滤条件
                if table_name:
                    query = query.filter(ComparisonResult.table_name == table_name)
                if status:
                    query = query.filter(ComparisonResult.status == status)
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 获取总记录数
                total_count = query.count()

                # 获取分页数据
                results_query = query.order_by(ComparisonResult.id.desc()).offset(offset).limit(limit)
                results = []

                for result in results_query:
                    results.append({
                        'id': result.id,
                        'record_key': result.record_key,
                        'status': result.status,
                        'field_name': result.field_name,
                        'value_a': result.source_value,
                        'value_b': result.target_value,
                        'created_at': result.created_at.isoformat() if result.created_at else None
                    })

                return results, total_count

        except Exception as e:
            logger.error(f"查询比对结果失败: {e}")
            return [], 0

    def search_records(
        self,
        table_name: str,
        search_term: str,
        page: int = 1,
        page_size: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        搜索包含指定关键词的记录 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名
            search_term: 搜索关键词
            page: 页码
            page_size: 每页记录数

        Returns:
            (结果列表, 总记录数)
        """
        try:
            # 委托给SQLAlchemy服务进行搜索
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import or_

                # 构建搜索查询
                search_pattern = f"%{search_term}%"
                query = session.query(ComparisonResult).filter(
                    ComparisonResult.table_name == table_name,
                    or_(
                        ComparisonResult.record_key.like(search_pattern),
                        ComparisonResult.source_value.like(search_pattern),
                        ComparisonResult.target_value.like(search_pattern),
                        ComparisonResult.field_name.like(search_pattern)
                    )
                )

                # 添加任务ID过滤
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 获取总记录数
                total_count = query.count()

                # 获取分页数据
                offset = (page - 1) * page_size
                results_query = query.order_by(ComparisonResult.id.desc()).offset(offset).limit(page_size)

                results = []
                for result in results_query:
                    results.append({
                        'id': result.id,
                        'record_key': result.record_key,
                        'status': result.status,
                        'field_name': result.field_name,
                        'value_a': result.source_value,
                        'value_b': result.target_value,
                        'created_at': result.created_at.isoformat() if result.created_at else None
                    })

                return results, total_count

        except Exception as e:
            logger.error(f"搜索记录失败: {e}")
            return [], 0

    def get_table_summary(self, table_name: str) -> Dict[str, int]:
        """
        获取表的差异统计摘要 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名

        Returns:
            差异统计字典
        """
        try:
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import func

                # 构建查询
                query = session.query(
                    ComparisonResult.status,
                    func.count(ComparisonResult.id).label('count')
                ).filter(ComparisonResult.table_name == table_name)

                # 添加任务ID过滤
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 分组统计
                results = query.group_by(ComparisonResult.status).all()

                summary = {}
                for status, count in results:
                    summary[status] = count

                return summary

        except Exception as e:
            logger.error(f"获取表摘要失败: {e}")
            return {}

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        batch_stats = self.batch_writer.get_stats() if hasattr(self, 'batch_writer') else {}

        return {
            'total_records': self.total_records,
            'batch_size': self.batch_size,
            'use_copy': self.use_copy,
            'high_performance_mode': self.high_performance_mode,
            'pg_config': {k: v for k, v in self.pg_config.items() if k != 'password'},  # 隐藏密码
            'table_name': self.table_name,
            'task_id': self.task_id,
            'stats': self.stats,
            'batch_writer_stats': batch_stats
        }

    def force_commit(self):
        """
        强制提交当前批处理数据
        用于确保数据及时写入数据库
        """
        if hasattr(self, 'batch_writer'):
            self.batch_writer.force_flush()
            logger.info(f"强制提交完成")

    def export_results_to_csv(self, output_path: str, table_name: str = None) -> bool:
        """
        导出结果到CSV文件

        Args:
            output_path: 输出文件路径
            table_name: 表名过滤

        Returns:
            是否导出成功
        """
        try:
            import csv

            # 获取所有结果数据
            results, _ = self.get_comparison_results(table_name=table_name, limit=1000000)

            if not results:
                return True

            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for result in results:
                    writer.writerow(result)

            logger.info(f"成功导出 {len(results)} 条记录到 {output_path}")
            return True

        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return False

    def create_indexes(self):
        """
        创建PostgreSQL优化索引
        提升查询性能
        """
        try:
            with self.service.get_db_session() as session:
                # 创建常用查询的索引
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_task_id ON comparison_results(task_id)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_table_name ON comparison_results(table_name)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_status ON comparison_results(status)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_record_key ON comparison_results(record_key)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_created_at ON comparison_results(created_at)",
                    "CREATE INDEX IF NOT EXISTS idx_comparison_results_composite ON comparison_results(task_id, table_name, status)"
                ]

                for index_sql in indexes:
                    try:
                        session.execute(index_sql)
                        logger.debug(f"创建索引: {index_sql}")
                    except Exception as e:
                        logger.warning(f"创建索引失败: {index_sql}, 错误: {e}")

                session.commit()
                logger.info("PostgreSQL索引创建完成")

        except Exception as e:
            logger.error(f"创建索引失败: {e}")

    def optimize_table(self):
        """
        优化PostgreSQL表
        执行VACUUM和ANALYZE
        """
        try:
            # 使用原生连接执行VACUUM（不能在事务中执行）
            if self.batch_writer.connection_pool:
                conn = self.batch_writer.connection_pool.getconn()
                try:
                    conn.autocommit = True
                    cursor = conn.cursor()
                    cursor.execute("VACUUM ANALYZE comparison_results")
                    logger.info("PostgreSQL表优化完成")
                finally:
                    conn.autocommit = False
                    self.batch_writer.connection_pool.putconn(conn)
            else:
                logger.warning("无法执行表优化：连接池不可用")

        except Exception as e:
            logger.error(f"表优化失败: {e}")

    # ==================== 任务管理API兼容性方法 ====================

    def create_comparison_task(self, name: str, description: str = None, **kwargs) -> Optional[str]:
        """
        创建新的比对任务（兼容性方法）

        Args:
            name: 任务名称
            description: 任务描述
            **kwargs: 其他任务参数

        Returns:
            任务ID
        """
        try:
            # 简化的任务创建逻辑（兼容性）
            task_id = f"task_{name}_{int(time.time())}"
            logger.info(f"创建比对任务: {name} (ID: {task_id})")
            logger.debug(f"任务描述: {description}")
            logger.debug(f"其他参数: {kwargs}")
            return task_id
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None

    def start_task(self, task_id: str = None):
        """
        开始执行任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"开始执行任务: {task_id}")
        else:
            logger.warning("未指定任务ID，无法启动任务")

    def complete_task(self, task_id: str = None, **summary):
        """
        完成任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            **summary: 任务摘要信息
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"完成任务: {task_id}, 摘要: {summary}")
        else:
            logger.warning("未指定任务ID，无法完成任务")

    def fail_task(self, task_id: str = None, error_msg: str = None):
        """
        标记任务失败（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            error_msg: 错误信息
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.error(f"任务失败: {task_id}, 错误: {error_msg}")
        else:
            logger.warning("未指定任务ID，无法标记任务失败")

    def update_task_progress(self, step_name: str, progress: float,
                           processed: int = 0, total: int = 0, message: str = None, task_id: str = None):
        """
        更新任务进度（兼容性方法）

        Args:
            step_name: 步骤名称
            progress: 进度百分比
            processed: 已处理记录数
            total: 总记录数
            message: 进度消息
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        task_id = task_id or self.task_id
        if task_id:
            log_msg = f"任务进度更新: {task_id} - {step_name} - {progress}% ({processed}/{total})"
            if message:
                log_msg += f" - {message}"
            logger.debug(log_msg)
        else:
            logger.warning("未指定任务ID，无法更新任务进度")

    def get_task_info(self, task_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取任务信息（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID

        Returns:
            任务信息字典或None
        """
        task_id = task_id or self.task_id
        if task_id:
            # 返回基本的任务信息
            return {
                'task_id': task_id,
                'status': 'running',
                'total_records': self.total_records,
                'created_at': datetime.now().isoformat()
            }
        return None
