#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite报告器：
- SQLAlchemy：用于数据库初始化、表结构管理、查询操作
- 原生 sqlite3：专门用于 comparison_results 表的高频插入操作
"""
import os
import time
import sqlite3
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

# 使用统一的数据模型和服务
from services.sqlalchemy_service import SQLAlchemyComparisonService
from reporters.base_reporter import BaseReporter
from core.models import DiffResult

# 配置日志
logger = logging.getLogger(__name__)


class Sqlite3BatchWriter:
    """SQLite批量写入器 - 精简版"""

    def __init__(self, service: SQLAlchemyComparisonService, config: Dict[str, Any]):
        self.service = service
        self.batch_size = config.get('batch_size', 10000)
        self.buffer = []
        self._lock = threading.Lock()
        self.total_records = 0

        # 事务管理
        self.transaction_batch_size = config.get('transaction_batch_size', 5)
        self._transaction_active = False
        self._batch_count = 0

        # 初始化原生SQLite连接
        self._init_native_sqlite_connection(config)

    def _init_native_sqlite_connection(self, config):
        """初始化原生SQLite连接以实现最佳插入性能"""
        try:
            # 从SQLAlchemy服务获取数据库路径
            db_url = str(self.service.engine.url)
            if db_url.startswith('sqlite:///'):
                self.db_path = db_url[10:]  # 移除 'sqlite:///' 前缀
            else:
                self.db_path = config.get('db_path', 'sqlcompare_v4.db')

            # 创建原生SQLite连接（专用于插入操作）
            self.native_conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0  # 30秒超时
            )

            # 优化SQLite性能设置
            self._configure_sqlite_performance(config)

            # 预编译插入语句
            self._prepare_insert_statement()

            # 事务管理
            self._transaction_active = False
            self._batch_count = 0

            logger.info(f"原生SQLite连接初始化完成: {self.db_path}")

        except Exception as e:
            logger.error(f"原生SQLite连接初始化失败: {e}")
            raise

    def _configure_sqlite_performance(self, config):
        """配置SQLite性能优化参数"""
        try:
            # 基础性能优化（保持数据安全性）
            self.native_conn.execute("PRAGMA journal_mode=WAL")        # WAL模式，支持并发读写
            self.native_conn.execute("PRAGMA synchronous=NORMAL")      # 平衡性能和安全性
            self.native_conn.execute("PRAGMA cache_size=50000")        # 增大缓存（约200MB）
            self.native_conn.execute("PRAGMA temp_store=MEMORY")       # 临时数据存储在内存
            self.native_conn.execute("PRAGMA mmap_size=268435456")     # 启用内存映射（256MB）

            # 高性能模式额外优化
            if config.get('high_performance_mode', True):
                self.native_conn.execute("PRAGMA cache_size=100000")   # 更大缓存（约400MB）
                self.native_conn.execute("PRAGMA mmap_size=536870912") # 更大内存映射（512MB）
                logger.info("启用高性能模式：增大缓存和内存映射")

            # 极致性能模式（谨慎使用）
            if config.get('extreme_performance_mode', False):
                self.native_conn.execute("PRAGMA synchronous=OFF")     # 关闭同步（有数据丢失风险）
                self.native_conn.execute("PRAGMA journal_mode=MEMORY") # 内存日志（有数据丢失风险）
                logger.warning("启用极致性能模式：存在数据丢失风险，仅建议在测试环境使用")

            self.native_conn.commit()

        except Exception as e:
            logger.error(f"SQLite性能配置失败: {e}")
            raise

    def _prepare_insert_statement(self):
        """预编译插入语句以提升性能"""
        self.insert_sql = """
            INSERT INTO comparison_results
            (task_id, table_name, record_key, status, field_name, source_value, target_value, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """

    def add_record(self, record: Dict[str, Any]):
        """添加记录到批处理缓冲区"""
        with self._lock:
            self.buffer.append(record)
            self.total_records += 1

            # 检查是否需要刷新批处理
            if len(self.buffer) >= self.batch_size:
                self._flush_batch_native()
            elif self.total_records % self.commit_interval == 0:
                current_time = time.time()
                if current_time - self.last_commit_time > 5:  # 至少5秒提交一次
                    self._flush_batch_native()
                    self.last_commit_time = current_time

    def _flush_batch_native(self):
        """使用原生SQLite进行高性能批量插入"""
        if not self.buffer:
            return

        try:
            batch_count = len(self.buffer)
            start_time = time.time()

            # 准备批量数据
            batch_data = self._prepare_batch_data()

            # 执行批量插入
            self._execute_batch_insert(batch_data)

            # 清空缓冲区
            self.buffer.clear()

            # 更新统计信息
            elapsed_time = time.time() - start_time

            logger.debug(f"原生SQLite批量插入完成: {batch_count} 条记录，耗时: {elapsed_time:.3f}秒")

        except Exception as e:
            logger.error(f"原生SQLite批量插入失败: {e}")
            # 清空缓冲区避免重复提交
            self.buffer.clear()
            # 回滚事务
            if self._transaction_active:
                self.native_conn.rollback()
                self._transaction_active = False
            raise

    def _prepare_batch_data(self) -> List[Tuple]:
        """准备批量插入数据"""
        batch_data = []
        current_time = datetime.now().isoformat()

        for record in self.buffer:
            batch_data.append((
                record.get('task_id'),
                record.get('table_name'),
                record.get('record_key'),
                record.get('status'),
                record.get('field_name'),
                record.get('source_value'),
                record.get('target_value'),
                current_time  # 统一的创建时间
            ))

        return batch_data

    def _execute_batch_insert(self, batch_data: List[Tuple]):
        """执行批量插入操作"""
        try:
            # 智能事务管理
            if not self._transaction_active:
                self.native_conn.execute("BEGIN IMMEDIATE")
                self._transaction_active = True

            # 使用预编译语句执行批量插入
            cursor = self.native_conn.cursor()
            cursor.executemany(self.insert_sql, batch_data)

            # 增加批次计数
            self._batch_count += 1

            # 根据批次数量决定是否提交事务
            if self._batch_count >= self.transaction_batch_size:
                self.native_conn.commit()
                self._transaction_active = False
                self._batch_count = 0
                logger.debug(f"事务提交: {self.transaction_batch_size} 个批次")

        except Exception as e:
            logger.error(f"批量插入执行失败: {e}")
            raise

    def force_flush(self):
        """强制刷新所有缓冲的记录"""
        with self._lock:
            if self.buffer:
                self._flush_batch_native()

        # 确保事务提交
        if self._transaction_active:
            self.native_conn.commit()
            self._transaction_active = False
            self._batch_count = 0
            logger.debug("强制提交事务完成")

    def close(self):
        """关闭批处理器"""
        try:
            # 强制刷新剩余数据
            self.force_flush()

            # 关闭原生SQLite连接
            if hasattr(self, 'native_conn'):
                self.native_conn.close()
                logger.debug("原生SQLite连接已关闭")

        except Exception as e:
            logger.error(f"关闭批处理器时出错: {e}")


class SqliteReporter(BaseReporter):
    """
    SQLite报告器架构特性：
        - SQLAlchemy：用于数据库初始化、表结构管理、查询操作
        - 原生 sqlite3：专门用于 comparison_results 表的高频插入操作
    """

    def __init__(self, config: Dict[str, Any]):
        """初始化SQLite报告器"""
        super().__init__(config)

        # 基础配置
        self.db_path = config.get('db_path', 'sqlcompare_v4.db')
        self.task_id = config.get('task_id') or f"task_{int(time.time())}"
        self.comparison_table = config.get('comparison_table', 'unknown')

        # 性能配置
        self.append_mode = config.get('append_mode', False)
        self.batch_size = config.get('batch_size', 10000)
        self.high_performance_mode = config.get('high_performance_mode', True)

        # 初始化SQLAlchemy服务
        database_url = f"sqlite:///{self.db_path}"
        self.service = SQLAlchemyComparisonService.get_instance(database_url)

        # 初始化高性能批处理器
        self.batch_writer = Sqlite3BatchWriter(self.service, config)

        # 向后兼容的属性
        self.total_records = 0
    
    def open(self):
        """
        打开报告目标 - 使用SQLAlchemy服务初始化
        """
        try:
            # 完全匹配原版本的文件处理逻辑
            if os.path.exists(self.db_path) and not self.append_mode:
                try:
                    os.remove(self.db_path)
                    logger.info(f"删除现有数据库文件: {self.db_path}")
                except (PermissionError, OSError) as e:
                    timestamp = int(time.time())
                    base_name, ext = os.path.splitext(self.db_path)
                    new_path = f"{base_name}_{timestamp}{ext}"
                    logger.warning(f"无法删除数据库文件，使用新路径: {new_path}")
                    self.db_path = new_path
                    # 重新初始化服务
                    self.service = SQLAlchemyComparisonService(
                        database_url=f"sqlite:///{self.db_path}",
                        lazy_init=True
                    )
            elif os.path.exists(self.db_path) and self.append_mode:
                # 在追加模式下，清理指定表的数据
                try:
                    with self.service.get_db_session() as session:
                        from models.sqlalchemy_models import ComparisonResult
                        session.query(ComparisonResult).filter(
                            ComparisonResult.table_name == self.comparison_table
                        ).delete()
                        session.commit()
                        logger.info(f"清理表 {self.comparison_table} 的现有数据")
                except Exception as e:
                    logger.warning(f"清理现有数据失败: {e}")
                    timestamp = int(time.time())
                    base_name, ext = os.path.splitext(self.db_path)
                    new_path = f"{base_name}_{timestamp}{ext}"
                    self.db_path = new_path
                    # 重新初始化服务
                    self.service = SQLAlchemyComparisonService(
                        database_url=f"sqlite:///{self.db_path}",
                        lazy_init=True
                    )

            # 确保SQLAlchemy服务的表结构已初始化
            self.service._ensure_tables_initialized()
            logger.info(f"UnifiedSQLiteReporter打开完成: {self.db_path}")

        except Exception as e:
            logger.error(f"报告器初始化失败: {e}")
            raise

    def _convert_diff_to_service_record(self, diff_result: DiffResult) -> Dict[str, Any]:
        """
        将DiffResult转换为SQLAlchemy服务兼容的记录格式

        Args:
            diff_result: 差异结果对象

        Returns:
            服务兼容的记录字典
        """
        # 基础记录信息
        base_record = {
            'task_id': self.task_id,
            'table_name': self.comparison_table,
            'record_key': diff_result.key,
            'status': diff_result.status,
            'field_name': None,
            'source_value': None,
            'target_value': None
        }

        # 处理字段级别的差异
        if hasattr(diff_result, 'field_diffs') and diff_result.field_diffs:
            # 返回多个记录，每个字段差异一个记录
            records = []
            for field_name, (value_a, value_b) in diff_result.field_diffs.items():
                record = base_record.copy()
                record.update({
                    'status': 'FIELD_DIFF',
                    'field_name': field_name,
                    'source_value': str(value_a) if value_a is not None else None,
                    'target_value': str(value_b) if value_b is not None else None
                })
                records.append(record)
            return records
        else:
            # 记录级别差异
            base_record.update({
                'source_value': str(getattr(diff_result, 'value_a', None)) if getattr(diff_result, 'value_a', None) is not None else None,
                'target_value': str(getattr(diff_result, 'value_b', None)) if getattr(diff_result, 'value_b', None) is not None else None
            })
            return [base_record]

    def close(self):
        """
        关闭报告目标
        """
        try:
            # 强制刷新批处理器中的剩余数据
            if hasattr(self, 'batch_writer'):
                self.batch_writer.force_flush()

            logger.info(f"UnifiedSQLiteReporter关闭，处理记录: {self.total_records}")

        except Exception as e:
            logger.error(f"关闭报告器时出错: {e}")

    def report_diff(self, diff_result: DiffResult):
        """
        记录一条差异 - 使用SQLAlchemy服务的高性能处理
        """
        try:
            # 转换为服务兼容的记录格式
            records = self._convert_diff_to_service_record(diff_result)

            # 使用高性能批处理器处理记录
            for record in records:
                self.batch_writer.add_record(record)
                self.total_records += 1

        except Exception as e:
            logger.error(f"报告差异失败: {e}")
            raise
    
    def get_comparison_results(
        self,
        table_name: str = None,
        status: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取比对结果 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名过滤
            difference_type: 差异类型过滤
            status: 状态过滤
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            (结果列表, 总记录数)
        """
        try:
            # 委托给SQLAlchemy服务进行查询
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import func

                # 构建查询
                query = session.query(ComparisonResult)

                # 添加过滤条件
                if table_name:
                    query = query.filter(ComparisonResult.table_name == table_name)
                if status:
                    query = query.filter(ComparisonResult.status == status)
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 获取总记录数
                total_count = query.count()

                # 获取分页数据
                results_query = query.order_by(ComparisonResult.id.desc()).offset(offset).limit(limit)
                results = []

                for result in results_query:
                    results.append({
                        'id': result.id,
                        'record_key': result.record_key,
                        'status': result.status,
                        'field_name': result.field_name,
                        'value_a': result.source_value,
                        'value_b': result.target_value,
                        'created_at': result.created_at.isoformat() if result.created_at else None
                    })

                return results, total_count

        except Exception as e:
            logger.error(f"查询比对结果失败: {e}")
            return [], 0

    def search_records(
        self,
        table_name: str,
        search_term: str,
        page: int = 1,
        page_size: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        搜索包含指定关键词的记录 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名
            search_term: 搜索关键词
            page: 页码
            page_size: 每页记录数

        Returns:
            (结果列表, 总记录数)
        """
        try:
            # 委托给SQLAlchemy服务进行搜索
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import or_

                # 构建搜索查询
                search_pattern = f"%{search_term}%"
                query = session.query(ComparisonResult).filter(
                    ComparisonResult.table_name == table_name,
                    or_(
                        ComparisonResult.record_key.like(search_pattern),
                        ComparisonResult.source_value.like(search_pattern),
                        ComparisonResult.target_value.like(search_pattern),
                        ComparisonResult.field_name.like(search_pattern)
                    )
                )

                # 添加任务ID过滤
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 获取总记录数
                total_count = query.count()

                # 获取分页数据
                offset = (page - 1) * page_size
                results_query = query.order_by(ComparisonResult.id.desc()).offset(offset).limit(page_size)

                results = []
                for result in results_query:
                    results.append({
                        'id': result.id,
                        'record_key': result.record_key,
                        'status': result.status,
                        'field_name': result.field_name,
                        'value_a': result.source_value,
                        'value_b': result.target_value,
                        'created_at': result.created_at.isoformat() if result.created_at else None
                    })

                return results, total_count

        except Exception as e:
            logger.error(f"搜索记录失败: {e}")
            return [], 0

    def get_table_summary(self, table_name: str) -> Dict[str, int]:
        """
        获取表的差异统计摘要 - 委托给SQLAlchemy服务

        Args:
            table_name: 表名

        Returns:
            差异统计字典
        """
        try:
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import func

                # 构建查询
                query = session.query(
                    ComparisonResult.status,
                    func.count(ComparisonResult.id).label('count')
                ).filter(ComparisonResult.table_name == table_name)

                # 添加任务ID过滤
                if self.task_id:
                    query = query.filter(ComparisonResult.task_id == self.task_id)

                # 分组统计
                results = query.group_by(ComparisonResult.status).all()

                summary = {}
                for status, count in results:
                    summary[status] = count

                return summary

        except Exception as e:
            logger.error(f"获取表摘要失败: {e}")
            return {}

    def force_commit(self):
        """
        强制提交当前批处理数据
        用于确保数据及时写入数据库
        """
        if hasattr(self, 'batch_writer'):
            self.batch_writer.force_flush()
            logger.info(f"强制提交完成")

    def export_results_to_csv(self, output_path: str, table_name: str = None) -> bool:
        """
        导出结果到CSV文件

        Args:
            output_path: 输出文件路径
            table_name: 表名过滤

        Returns:
            是否导出成功
        """
        try:
            import csv

            # 获取所有结果数据
            results, _ = self.get_comparison_results(table_name=table_name, limit=1000000)

            if not results:
                return True

            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for result in results:
                    writer.writerow(result)

            logger.info(f"成功导出 {len(results)} 条记录到 {output_path}")
            return True

        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return False

    # ==================== 任务管理API兼容性方法 ====================

    def create_comparison_task(self, name: str, description: str = None, **kwargs) -> Optional[str]:
        """
        创建新的比对任务（兼容性方法）

        Args:
            name: 任务名称
            description: 任务描述
            **kwargs: 其他任务参数

        Returns:
            任务ID
        """
        try:
            # 简化的任务创建逻辑（兼容性）
            task_id = f"task_{name}_{int(time.time())}"
            logger.info(f"创建比对任务: {name} (ID: {task_id})")
            logger.debug(f"任务描述: {description}")
            logger.debug(f"其他参数: {kwargs}")
            return task_id
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None

    def start_task(self, task_id: str = None):
        """
        开始执行任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"开始执行任务: {task_id}")
        else:
            logger.warning("未指定任务ID，无法启动任务")

    def complete_task(self, task_id: str = None, **summary):
        """
        完成任务（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            **summary: 任务摘要信息
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"完成任务: {task_id}, 摘要: {summary}")
        else:
            logger.warning("未指定任务ID，无法完成任务")

    def fail_task(self, task_id: str = None, error_msg: str = None):
        """
        标记任务失败（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID
            error_msg: 错误信息
        """
        task_id = task_id or self.task_id
        if task_id:
            logger.error(f"任务失败: {task_id}, 错误: {error_msg}")
        else:
            logger.warning("未指定任务ID，无法标记任务失败")

    def update_task_progress(self, step_name: str, progress: float,
                           processed: int = 0, total: int = 0, message: str = None, task_id: str = None):
        """
        更新任务进度（兼容性方法）

        Args:
            step_name: 步骤名称
            progress: 进度百分比
            processed: 已处理记录数
            total: 总记录数
            message: 进度消息
            task_id: 任务ID，如果为None则使用当前任务ID
        """
        task_id = task_id or self.task_id
        if task_id:
            log_msg = f"任务进度更新: {task_id} - {step_name} - {progress}% ({processed}/{total})"
            if message:
                log_msg += f" - {message}"
            logger.debug(log_msg)
        else:
            logger.warning("未指定任务ID，无法更新任务进度")

    def get_task_info(self, task_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取任务信息（兼容性方法）

        Args:
            task_id: 任务ID，如果为None则使用当前任务ID

        Returns:
            任务信息字典或None
        """
        task_id = task_id or self.task_id
        if task_id:
            # 返回基本的任务信息
            return {
                'task_id': task_id,
                'status': 'running',
                'total_records': self.total_records,
                'created_at': datetime.now().isoformat()
            }
        return None
    


    # ==================== 任务管理API兼容性方法 ====================

    def create_comparison_task(self, name: str, description: str = None, **kwargs) -> Optional[str]:
        """创建新的比对任务（兼容性方法）"""
        task_id = f"task_{name}_{int(time.time())}"
        logger.info(f"创建比对任务: {name} (ID: {task_id})")
        if description:
            logger.debug(f"任务描述: {description}")
        if kwargs:
            logger.debug(f"其他参数: {kwargs}")
        return task_id

    def start_task(self, task_id: str = None):
        """开始执行任务（兼容性方法）"""
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"开始执行任务: {task_id}")

    def complete_task(self, task_id: str = None, **summary):
        """完成任务（兼容性方法）"""
        task_id = task_id or self.task_id
        if task_id:
            logger.info(f"完成任务: {task_id}, 摘要: {summary}")

    def fail_task(self, task_id: str = None, error_msg: str = None):
        """标记任务失败（兼容性方法）"""
        task_id = task_id or self.task_id
        if task_id:
            logger.error(f"任务失败: {task_id}, 错误: {error_msg}")

    def update_task_progress(self, step_name: str, progress: float,
                           processed: int = 0, total: int = 0, message: str = None, task_id: str = None):
        """更新任务进度（兼容性方法）"""
        task_id = task_id or self.task_id
        if task_id:
            log_msg = f"任务进度更新: {task_id} - {step_name} - {progress}% ({processed}/{total})"
            if message:
                log_msg += f" - {message}"
            logger.debug(log_msg)

    def get_task_info(self, task_id: str = None) -> Optional[Dict[str, Any]]:
        """获取任务信息（兼容性方法）"""
        task_id = task_id or self.task_id
        if task_id:
            return {
                'task_id': task_id,
                'status': 'running',
                'total_records': self.total_records,
                'created_at': datetime.now().isoformat()
            }
        return None
