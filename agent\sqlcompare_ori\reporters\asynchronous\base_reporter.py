# reporters/asynchronous/base_reporter.py
"""
异步差异报告器抽象基类
支持异步写入和批量处理
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from core.models import DiffResult


class AsyncBaseReporter(ABC):
    """异步差异报告器抽象基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, batch_size: int = 100):
        """
        初始化异步报告器
        
        Args:
            config: 报告器配置
            batch_size: 批量写入大小
        """
        self.config = config or {}
        self.batch_size = batch_size
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 批量缓冲区
        self._buffer: List[DiffResult] = []
        self._buffer_lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            'total_diffs_reported': 0,
            'total_batches_written': 0,
            'total_write_time': 0.0,
            'last_error': None
        }
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化报告器
        
        Returns:
            初始化是否成功
        """
        pass
    
    @abstractmethod
    async def finalize(self) -> bool:
        """
        完成报告器处理
        
        Returns:
            完成是否成功
        """
        pass
    
    @abstractmethod
    async def _write_batch_async(self, diffs: List[DiffResult]) -> bool:
        """
        异步批量写入差异
        
        Args:
            diffs: 差异列表
            
        Returns:
            写入是否成功
        """
        pass
    
    async def report_diff_async(self, diff: DiffResult) -> bool:
        """
        异步报告单个差异
        
        Args:
            diff: 差异结果
            
        Returns:
            报告是否成功
        """
        async with self._buffer_lock:
            self._buffer.append(diff)
            
            # 如果缓冲区满了，触发批量写入
            if len(self._buffer) >= self.batch_size:
                await self._flush_buffer()
        
        return True
    
    async def report_diffs_async(self, diffs: List[DiffResult]) -> bool:
        """
        异步报告多个差异
        
        Args:
            diffs: 差异列表
            
        Returns:
            报告是否成功
        """
        for diff in diffs:
            await self.report_diff_async(diff)
        return True
    
    async def flush(self) -> bool:
        """
        强制刷新缓冲区
        
        Returns:
            刷新是否成功
        """
        async with self._buffer_lock:
            await self._flush_buffer()
        return True
    
    async def _flush_buffer(self):
        """内部刷新缓冲区方法"""
        if not self._buffer:
            return
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 复制缓冲区内容并清空
            batch_to_write = self._buffer.copy()
            self._buffer.clear()
            
            # 异步写入
            success = await self._write_batch_async(batch_to_write)
            
            if success:
                # 更新统计信息
                write_time = asyncio.get_event_loop().time() - start_time
                self.stats['total_diffs_reported'] += len(batch_to_write)
                self.stats['total_batches_written'] += 1
                self.stats['total_write_time'] += write_time
            else:
                # 写入失败，将数据重新放回缓冲区
                self._buffer.extend(batch_to_write)
                
        except Exception as e:
            self.logger.error(f"批量写入失败: {e}")
            self.stats['last_error'] = str(e)
            # 写入失败，将数据重新放回缓冲区
            self._buffer.extend(batch_to_write)
            raise
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 确保所有缓冲的数据都被写入
        await self.flush()
        await self.finalize()
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        async with self._buffer_lock:
            return {
                **self.stats,
                'buffer_size': len(self._buffer),
                'batch_size': self.batch_size
            }
    
    async def set_batch_size(self, batch_size: int):
        """
        动态设置批量大小
        
        Args:
            batch_size: 新的批量大小
        """
        if batch_size > 0:
            async with self._buffer_lock:
                # 如果当前缓冲区大小超过新的批量大小，先刷新
                if len(self._buffer) >= batch_size:
                    await self._flush_buffer()
                self.batch_size = batch_size
                self.logger.info(f"批量大小已更新为: {batch_size}")
        else:
            raise ValueError("批量大小必须大于0")


class AsyncFileReporter(AsyncBaseReporter):
    """异步文件报告器基类"""
    
    def __init__(self, filepath: str, **kwargs):
        """
        初始化异步文件报告器
        
        Args:
            filepath: 文件路径
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        self.filepath = filepath
        self._file_handle = None
    
    async def initialize(self) -> bool:
        """初始化文件报告器"""
        try:
            import aiofiles
            self._file_handle = await aiofiles.open(self.filepath, 'w', encoding='utf-8')
            await self._write_header()
            return True
        except Exception as e:
            self.logger.error(f"文件报告器初始化失败: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    async def finalize(self) -> bool:
        """完成文件报告器处理"""
        try:
            if self._file_handle:
                await self._write_footer()
                await self._file_handle.close()
                self._file_handle = None
            return True
        except Exception as e:
            self.logger.error(f"文件报告器完成失败: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    @abstractmethod
    async def _write_header(self):
        """写入文件头部"""
        pass
    
    @abstractmethod
    async def _write_footer(self):
        """写入文件尾部"""
        pass


class AsyncDatabaseReporter(AsyncBaseReporter):
    """异步数据库报告器基类"""
    
    def __init__(self, db_config: Dict[str, Any], table_name: str, **kwargs):
        """
        初始化异步数据库报告器
        
        Args:
            db_config: 数据库配置
            table_name: 表名
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        self.db_config = db_config
        self.table_name = table_name
        self._connection = None
    
    @abstractmethod
    async def _connect_database(self):
        """连接数据库"""
        pass
    
    @abstractmethod
    async def _disconnect_database(self):
        """断开数据库连接"""
        pass
    
    @abstractmethod
    async def _create_table_if_not_exists(self):
        """创建表（如果不存在）"""
        pass
    
    async def initialize(self) -> bool:
        """初始化数据库报告器"""
        try:
            await self._connect_database()
            await self._create_table_if_not_exists()
            return True
        except Exception as e:
            self.logger.error(f"数据库报告器初始化失败: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    async def finalize(self) -> bool:
        """完成数据库报告器处理"""
        try:
            await self._disconnect_database()
            return True
        except Exception as e:
            self.logger.error(f"数据库报告器完成失败: {e}")
            self.stats['last_error'] = str(e)
            return False


# 工厂函数
def create_async_reporter(reporter_type: str, **kwargs) -> AsyncBaseReporter:
    """
    创建异步报告器的工厂函数
    
    Args:
        reporter_type: 报告器类型
        **kwargs: 报告器参数
        
    Returns:
        异步报告器实例
    """
    # 这里可以根据reporter_type创建不同类型的报告器
    # 具体实现将在各个报告器中完成
    raise NotImplementedError(f"不支持的报告器类型: {reporter_type}")
