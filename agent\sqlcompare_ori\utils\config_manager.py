#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能配置管理器模块
支持自动发现、缓存和快速加载配置文件
"""

import os
import time
import configparser
import xml.etree.ElementTree as ET
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class SmartConfigManager:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(SmartConfigManager, cls).__new__(cls, *args, **kwargs)
        return cls._instance
    """智能配置管理器 - 支持自动发现、缓存和快速加载配置文件"""

    def __init__(self):
        # 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
        self._initialized = True

        self.config = None
        self.rules = None
        self.config_file = None
        self.rules_file = None

        # 性能优化：添加配置缓存机制
        self._rules_cache = {}
        self._config_cache = {}
        self._cache_timestamp = {}
        self._cache_ttl = 604800

        self._cache_hits = 0
        self._cache_misses = 0

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = cls()
        return cls._instance

    def _is_cache_valid(self, file_path: str) -> bool:
        """检查缓存是否有效"""
        if file_path not in self._cache_timestamp:
            return False

        cache_time = self._cache_timestamp[file_path]
        current_time = time.time()

        # 检查缓存是否过期
        if current_time - cache_time > self._cache_ttl:
            return False

        # 检查文件是否被修改
        try:
            file_mtime = os.path.getmtime(file_path)
            return file_mtime <= cache_time
        except OSError:
            return False

    def _load_from_cache(self, config_file: str) -> bool:
        """从缓存加载配置"""
        try:
            cached_data = self._config_cache.get(config_file)
            if cached_data:
                self.config = cached_data['config']
                self.rules = cached_data['rules']
                self.config_file = config_file
                self.rules_file = cached_data['rules_file']
                self._cache_hits += 1
                return True
        except Exception as e:
            logger.info(f"⚠️ 缓存加载失败: {e}")

        self._cache_misses += 1
        return False

    def _save_to_cache(self, config_file: str):
        """保存配置到缓存"""
        try:
            self._config_cache[config_file] = {
                'config': self.config,
                'rules': self.rules,
                'rules_file': self.rules_file
            }
            self._cache_timestamp[config_file] = time.time()
        except Exception as e:
            logger.info(f"⚠️ 缓存保存失败: {e}")

    @staticmethod
    def auto_discover_configs(type: str = '.ini') -> List[str]:
        """自动发现配置文件"""
        config_files = []

        # 搜索路径
        current_dir = os.path.dirname(__file__)
        search_paths = [
            os.path.join(os.getcwd(), "config"),
            # 当前项目配置
            os.path.join(current_dir, "config"),
            os.path.join(current_dir, "..", "config"),
            os.path.join(current_dir, "..", "..", "config")            
        ]

        for search_path in search_paths:
            abs_search_path = os.path.abspath(search_path)
            if os.path.exists(abs_search_path):
                try:
                    for file in os.listdir(abs_search_path):
                        if file.endswith(type):
                            full_path = os.path.join(abs_search_path, file)
                            config_files.append(full_path)
                except Exception as e:
                    logger.warning(f"⚠️ 搜索路径失败 {abs_search_path}: {e}")
            else:
                logger.debug(f"❌ 路径不存在: {abs_search_path}")

        return config_files

    def load_config(self, config_file: str) -> bool:
        """加载配置文件（支持缓存）"""
        if self._is_cache_valid(config_file):
            return self._load_from_cache(config_file)

        try:
            # 检查文件是否存在
            if not os.path.exists(config_file):
                logger.error(f"❌ 配置文件不存在: {config_file}")
                return False

            self.config = configparser.ConfigParser()
            # 保持配置键的原始大小写，不转换为小写
            # self.config.optionxform = str
            self.config.read(config_file, encoding='utf-8')
            self.config_file = config_file

            # 验证必要的配置节
            required_sections = ['COMMON', 'DB1', 'DB2']
            for section in required_sections:
                if not self.config.has_section(section):
                    logger.error(f"❌ 配置文件缺少必要的节: {section}")
                    logger.error(f"📋 实际存在的节: {list(self.config.sections())}")
                    raise ValueError(f"配置文件缺少必要的节: {section}")

            # 加载规则文件
            rules_file = self.config.get('COMMON', 'TAB_RULE')
            if not os.path.isabs(rules_file):
                rules_file = os.path.join(os.path.dirname(config_file), rules_file)

            if not os.path.exists(rules_file):
                logger.error(f"❌ 规则文件不存在: {rules_file}")
                return False

            success = self.load_rules(rules_file)

            # 保存到缓存
            if success:
                self._save_to_cache(config_file)

            return success

        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            return False

    def load_rules(self, rules_file: str) -> bool:
        """加载规则文件（支持缓存）"""
        if rules_file in self._rules_cache:
            cache_time = self._rules_cache[rules_file].get('timestamp', 0)
            try:
                file_mtime = os.path.getmtime(rules_file)
                if file_mtime <= cache_time:
                    self.rules = self._rules_cache[rules_file]['rules']
                    self.rules_file = rules_file
                    return True
            except OSError:
                pass

        try:
            tree = ET.parse(rules_file)
            self.rules = tree.getroot()
            self.rules_file = rules_file

            # 缓存规则
            self._rules_cache[rules_file] = {
                'rules': self.rules,
                'timestamp': time.time()
            }

            return True
        except Exception as e:
            logger.error(f"加载规则文件失败: {str(e)}")
            return False

    def get_database_config(self, db_key: str):
        """获取数据库配置"""
        if not self.config or not self.config.has_section(db_key):
            return None

        section = self.config[db_key]

        # 创建简化的数据库配置对象
        class DatabaseConfig:
            def __init__(self, db_type, host, port, username, password, database):
                self.db_type = db_type
                self.host = host
                self.port = port
                self.username = username
                self.password = password
                self.database = database

        return DatabaseConfig(
            db_type=section.get('TYPE', 'db2').lower(),
            host=section.get('IP'),
            port=int(section.get('PORT', 50000)),
            username=section.get('USER_NAME'),
            password=section.get('PASSWORD'),
            database=section.get('SCHEMA')
        )

    def get_comparison_tables(self) -> List[Dict[str, Any]]:
        """获取比对表配置"""
        tables = []
        if self.rules is not None:
            for table in self.rules.findall('table'):
                table_info = {
                    'table_id': table.get('table_id'),
                    'remark': table.get('remark', ''),
                    'sql_1': table.find('sql_1').text.strip() if table.find('sql_1') is not None else '',
                    'sql_2': table.find('sql_2').text.strip() if table.find('sql_2') is not None else ''
                }
                tables.append(table_info)
        return tables

    def get_title(self) -> str:
        """获取应用程序标题"""
        if self.config and self.config.has_option('COMMON', 'TITLE'):
            return self.config.get('COMMON', 'TITLE')
        return "数据库比对工具"

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        hit_rate = 0
        if self._cache_hits + self._cache_misses > 0:
            hit_rate = self._cache_hits / (self._cache_hits + self._cache_misses) * 100

        return {
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'hit_rate': f"{hit_rate:.1f}%",
            'cached_configs': len(self._config_cache),
            'cached_rules': len(self._rules_cache)
        }

    def clear_cache(self):
        """清空所有缓存"""
        self._config_cache.clear()
        self._cache_timestamp.clear()
        self._rules_cache.clear()
        self._cache_hits = 0
        self._cache_misses = 0
        logger.info("🗑️ 配置缓存已清空")

    def get_database_config_dict(self, db_key: str) -> Dict[str, Any]:
        """获取数据库配置字典（用于需要原始配置的场景）"""
        if not self.config or not self.config.has_section(db_key):
            return {}

        config_dict = {}
        for key, value in self.config.items(db_key):
            config_dict[key] = value
        return config_dict
