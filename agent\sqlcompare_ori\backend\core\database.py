#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置和连接管理 - 委托给主项目的数据库管理
"""

import os
import sys
import logging
from typing import AsyncGenerator
from pathlib import Path

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 直接使用主项目的数据库管理
from models.sqlalchemy_models import Base, create_database_engine, get_session_factory
from services.sqlalchemy_service import SQLAlchemyComparisonService

logger = logging.getLogger(__name__)

# 全局变量 - 委托给主项目
_sqlalchemy_service = None


def get_sqlalchemy_service(database_url: str = "sqlite:///./sqlcompare.db") -> SQLAlchemyComparisonService:
    """获取SQLAlchemy服务实例"""
    global _sqlalchemy_service

    if not _sqlalchemy_service:
        _sqlalchemy_service = SQLAlchemyComparisonService(database_url)

    return _sqlalchemy_service


def get_db():
    """获取数据库会话 - 委托给主项目服务"""
    service = get_sqlalchemy_service()
    with service.get_db_session() as session:
        yield session


async def get_db_async() -> AsyncGenerator:
    """异步获取数据库会话 - 委托给主项目服务"""
    service = get_sqlalchemy_service()
    with service.get_db_session() as session:
        yield session


async def init_database(database_url: str = "sqlite:///./sqlcompare.db"):
    """初始化数据库 - 委托给主项目服务"""
    try:
        logger.info("正在初始化数据库...")

        # 直接使用主项目的服务初始化
        service = get_sqlalchemy_service(database_url)

        logger.info("数据库初始化完成")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def close_database():
    """关闭数据库连接 - 委托给主项目服务"""
    global _sqlalchemy_service

    try:
        # 主项目服务会自动管理连接
        _sqlalchemy_service = None

        logger.info("数据库连接已关闭")

    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}")


class DatabaseManager:
    """数据库管理器 - 委托给主项目服务"""

    def __init__(self, database_url: str = "sqlite:///./sqlcompare.db"):
        self.database_url = database_url
        self._service = None

    async def initialize(self):
        """初始化数据库管理器"""
        await init_database(self.database_url)
        self._service = get_sqlalchemy_service(self.database_url)

    async def close(self):
        """关闭数据库管理器"""
        await close_database()

    def get_service(self) -> SQLAlchemyComparisonService:
        """获取SQLAlchemy服务"""
        if not self._service:
            self._service = get_sqlalchemy_service(self.database_url)
        return self._service


# 全局数据库管理器实例
db_manager = DatabaseManager()
