#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLCompare Backend API - 修正版主应用
Backend API作为SQLCompare核心功能的轻量级服务包装层
"""

import os
import sys
import logging
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 直接使用SQLCompare的组件（不通过适配器）
from models.service_models import TaskStatus
from backend.api.v1.tasks_corrected import router as tasks_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="SQLCompare API",
    description="SQLCompare数据库比对引擎的RESTful API服务包装层",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("SQLCompare Backend API 启动中...")
    
    # 验证SQLCompare核心组件可用性
    try:
        from core.engine import compare_sources_memory_dict
        from reporters.sqlite_reporter import TaskManager
        from utils.config_manager import SmartConfigManager
        
        logger.info("✅ SQLCompare核心组件验证通过")
        
        # 测试TaskManager
        task_manager = TaskManager()
        logger.info("✅ TaskManager初始化成功")
        
    except ImportError as e:
        logger.error(f"❌ SQLCompare核心组件导入失败: {e}")
        raise RuntimeError("SQLCompare核心组件不可用")
    except Exception as e:
        logger.error(f"❌ SQLCompare核心组件验证失败: {e}")
        raise RuntimeError(f"SQLCompare核心组件验证失败: {e}")
    
    logger.info("🚀 SQLCompare Backend API 启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("SQLCompare Backend API 正在关闭...")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "error": str(exc)
        }
    )


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "SQLCompare Backend API",
        "description": "SQLCompare数据库比对引擎的RESTful API服务包装层",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查SQLCompare核心组件
        from reporters.sqlite_reporter import TaskManager
        
        task_manager = TaskManager()
        
        # 简单的功能测试
        test_tasks = task_manager.get_task_list(limit=1)
        
        return {
            "status": "healthy",
            "message": "SQLCompare Backend API 运行正常",
            "sqlcompare_core": "available",
            "task_manager": "functional"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "message": "SQLCompare Backend API 运行异常",
                "error": str(e)
            }
        )


@app.get("/info")
async def get_info():
    """获取系统信息"""
    try:
        # 获取SQLCompare核心信息
        from reporters.sqlite_reporter import TaskManager
        
        task_manager = TaskManager()
        
        # 获取任务统计
        all_tasks = task_manager.get_task_list(limit=1000)
        task_stats = {}
        
        for task in all_tasks:
            status = task.get('status', 'unknown')
            task_stats[status] = task_stats.get(status, 0) + 1
        
        # 获取可用的连接器
        available_connectors = []
        try:
            from connectors.db2_connector import DB2Connector
            available_connectors.append("DB2")
        except ImportError:
            pass
        
        try:
            from connectors.mysql_connector import MySQLConnector
            available_connectors.append("MySQL")
        except ImportError:
            pass
        
        return {
            "service": "SQLCompare Backend API",
            "version": "1.0.0",
            "architecture": "Service Wrapper for SQLCompare Core",
            "sqlcompare_core": {
                "status": "available",
                "task_statistics": task_stats,
                "available_connectors": available_connectors
            },
            "api_features": [
                "Task Management",
                "Progress Monitoring", 
                "Result Querying",
                "Real-time Status"
            ]
        }
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 包含API路由
app.include_router(
    tasks_router,
    prefix="/api/v1/tasks",
    tags=["任务管理"]
)


# 简化的结果查询API
@app.get("/api/v1/results/{task_id}")
async def get_task_results(task_id: str, limit: int = 100):
    """获取任务比对结果 - 直接使用SQLCompare核心"""
    try:
        from reporters.sqlite_reporter import SqliteReporter
        
        # 直接使用SQLCompare的结果查询
        reporter = SqliteReporter(task_id=task_id)
        results = reporter.get_comparison_results(limit=limit)
        
        return {
            "success": True,
            "message": "获取比对结果成功",
            "data": {
                "task_id": task_id,
                "results": results,
                "count": len(results)
            }
        }
        
    except Exception as e:
        logger.error(f"获取比对结果失败: {task_id}, {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 简化的统计API
@app.get("/api/v1/statistics")
async def get_statistics():
    """获取系统统计信息 - 直接使用SQLCompare核心"""
    try:
        from reporters.sqlite_reporter import TaskManager
        
        task_manager = TaskManager()
        all_tasks = task_manager.get_task_list(limit=10000)
        
        # 统计各状态任务数量
        stats = {
            "total_tasks": len(all_tasks),
            "pending_tasks": len([t for t in all_tasks if t.get('status') == TaskStatus.PENDING]),
            "running_tasks": len([t for t in all_tasks if t.get('status') == TaskStatus.RUNNING]),
            "completed_tasks": len([t for t in all_tasks if t.get('status') == TaskStatus.COMPLETED]),
            "failed_tasks": len([t for t in all_tasks if t.get('status') == TaskStatus.FAILED]),
            "cancelled_tasks": len([t for t in all_tasks if t.get('status') == TaskStatus.CANCELLED])
        }
        
        return {
            "success": True,
            "message": "获取统计信息成功",
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    
    # 启动开发服务器
    uvicorn.run(
        "main_corrected:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
