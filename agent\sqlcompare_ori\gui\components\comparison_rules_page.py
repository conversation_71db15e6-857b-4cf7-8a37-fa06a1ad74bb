#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比对规则配置页面组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
import configparser
from pathlib import Path
from .base_page import BasePage


class ComparisonRulesPage(BasePage):
    """比对规则配置页面"""
    
    def __init__(self, parent, app_instance=None):
        # 比对选项变量
        self.ignore_case_var = tk.BooleanVar(value=False)
        self.ignore_whitespace_var = tk.BooleanVar(value=True)
        self.ignore_null_var = tk.BooleanVar(value=False)
        self.fuzzy_match_var = tk.BooleanVar(value=False)
        
        # 高级设置变量
        self.numeric_precision_var = tk.StringVar(value="2")
        self.timeout_var = tk.StringVar(value="300")
        self.batch_size_var = tk.StringVar(value="1000")
        self.parallel_threads_var = tk.StringVar(value="4")
        
        super().__init__(parent, app_instance)

        # 加载配置文件
        self._load_config_file()
    
    def _create_ui(self):
        """创建比对规则配置界面"""
        self.frame = ttk.Frame(self.parent)
        
        # 页面标题
        title_label = ttk.Label(self.frame, text="比对规则配置", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(20, 30))
        
        # 创建主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=40, pady=20)
        
        # 基本比对选项
        self._create_basic_options_section(main_container)
        
        # 数据类型处理规则
        self._create_data_type_rules_section(main_container)
        
        # 性能优化设置
        self._create_performance_section(main_container)
        
        # TAB_RULE配置
        self._create_tab_rule_section(main_container)
        
        # 操作按钮
        self._create_action_buttons(main_container)
    
    def _create_basic_options_section(self, parent):
        """创建基本比对选项区域"""
        options_frame = ttk.LabelFrame(parent, text="基本比对选项", padding=15)
        options_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 比对选项
        options = [
            (self.ignore_case_var, "忽略大小写", "比对时忽略字符串的大小写差异"),
            (self.ignore_whitespace_var, "忽略前后空格", "比对时忽略字符串前后的空格"),
            (self.ignore_null_var, "忽略NULL值差异", "将NULL和空字符串视为相同"),
            (self.fuzzy_match_var, "启用模糊匹配", "对相似度高的记录进行模糊匹配")
        ]
        
        for i, (var, text, desc) in enumerate(options):
            option_frame = ttk.Frame(options_frame)
            option_frame.pack(fill=tk.X, pady=5)
            
            ttk.Checkbutton(option_frame, text=text, variable=var).pack(side=tk.LEFT)
            ttk.Label(option_frame, text=f"- {desc}", foreground="gray").pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_data_type_rules_section(self, parent):
        """创建数据类型处理规则区域"""
        rules_frame = ttk.LabelFrame(parent, text="数据类型处理规则", padding=15)
        rules_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 数值精度设置
        numeric_frame = ttk.Frame(rules_frame)
        numeric_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(numeric_frame, text="数值比对精度(小数位):").pack(side=tk.LEFT)
        numeric_entry = ttk.Entry(numeric_frame, textvariable=self.numeric_precision_var, width=10)
        numeric_entry.pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(numeric_frame, text="位").pack(side=tk.LEFT)
        
        # 日期时间格式
        datetime_frame = ttk.Frame(rules_frame)
        datetime_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(datetime_frame, text="日期时间格式:").pack(side=tk.LEFT)
        self.datetime_format_var = tk.StringVar(value="yyyy-MM-dd HH:mm:ss")
        datetime_combo = ttk.Combobox(datetime_frame, textvariable=self.datetime_format_var,
                                     values=["yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "dd/MM/yyyy HH:mm:ss"],
                                     width=25)
        datetime_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # 字符编码处理
        encoding_frame = ttk.Frame(rules_frame)
        encoding_frame.pack(fill=tk.X)
        
        ttk.Label(encoding_frame, text="字符编码处理:").pack(side=tk.LEFT)
        self.encoding_handling_var = tk.StringVar(value="自动检测")
        encoding_combo = ttk.Combobox(encoding_frame, textvariable=self.encoding_handling_var,
                                     values=["自动检测", "UTF-8", "GBK", "ASCII"],
                                     width=15, state="readonly")
        encoding_combo.pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_performance_section(self, parent):
        """创建性能优化设置区域"""
        perf_frame = ttk.LabelFrame(parent, text="性能优化设置", padding=15)
        perf_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 第一行：超时时间和批处理大小
        row1_frame = ttk.Frame(perf_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(row1_frame, text="超时时间(秒):").pack(side=tk.LEFT)
        timeout_entry = ttk.Entry(row1_frame, textvariable=self.timeout_var, width=10)
        timeout_entry.pack(side=tk.LEFT, padx=(10, 20))
        
        ttk.Label(row1_frame, text="批处理大小:").pack(side=tk.LEFT)
        batch_entry = ttk.Entry(row1_frame, textvariable=self.batch_size_var, width=10)
        batch_entry.pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(row1_frame, text="条记录").pack(side=tk.LEFT)
        
        # 第二行：并行线程数
        row2_frame = ttk.Frame(perf_frame)
        row2_frame.pack(fill=tk.X)
        
        ttk.Label(row2_frame, text="并行线程数:").pack(side=tk.LEFT)
        threads_entry = ttk.Entry(row2_frame, textvariable=self.parallel_threads_var, width=10)
        threads_entry.pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(row2_frame, text="个").pack(side=tk.LEFT)
        
        # 内存优化选项
        self.memory_optimize_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2_frame, text="启用内存优化", 
                       variable=self.memory_optimize_var).pack(side=tk.RIGHT)
    
    def _create_tab_rule_section(self, parent):
        """创建TAB_RULE配置区域"""
        tab_rule_frame = ttk.LabelFrame(parent, text="表比对规则 (TAB_RULE)", padding=15)
        tab_rule_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 说明文本
        info_label = ttk.Label(tab_rule_frame, 
                              text="配置每个表的具体比对SQL规则，支持自定义WHERE条件和字段映射",
                              foreground="gray")
        info_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 创建文本编辑区域
        text_frame = ttk.Frame(tab_rule_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文本编辑器
        self.tab_rule_text = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10), height=8)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.tab_rule_text.yview)
        self.tab_rule_text.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tab_rule_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 默认内容
        default_content = """# TAB_RULE配置示例
# 格式: 表名 = SQL查询语句

user_table = SELECT id, name, email, created_at FROM users WHERE status = 'active'
order_table = SELECT order_id, user_id, amount, order_date FROM orders WHERE order_date >= '2024-01-01'
product_table = SELECT product_id, name, price, category FROM products

# 支持的配置选项:
# - 使用WHERE条件过滤数据
# - 指定要比对的字段
# - 设置字段别名进行映射
"""
        self.tab_rule_text.insert(tk.END, default_content)
        
        # 按钮区域
        btn_frame = ttk.Frame(tab_rule_frame)
        btn_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(btn_frame, text="验证语法", 
                  command=self._validate_tab_rule).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="格式化", 
                  command=self._format_tab_rule).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="清空", 
                  command=self._clear_tab_rule).pack(side=tk.LEFT)
        ttk.Button(btn_frame, text="加载示例", 
                  command=self._load_example_tab_rule).pack(side=tk.RIGHT)
    
    def _create_action_buttons(self, parent):
        """创建操作按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(btn_frame, text="保存配置", 
                  command=self._save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="加载配置", 
                  command=self._load_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="重置为默认", 
                  command=self._reset_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="测试规则", 
                  command=self._test_rules).pack(side=tk.RIGHT)
    
    def _validate_tab_rule(self):
        """验证TAB_RULE语法"""
        content = self.tab_rule_text.get(1.0, tk.END).strip()
        
        if not content:
            messagebox.showwarning("警告", "TAB_RULE内容为空")
            return
        
        try:
            # 简单的语法验证
            lines = content.split('\n')
            valid_lines = 0
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    if '=' in line:
                        table_name, sql = line.split('=', 1)
                        table_name = table_name.strip()
                        sql = sql.strip()
                        
                        if not table_name or not sql:
                            raise ValueError(f"无效的配置行: {line}")
                        
                        if not sql.upper().startswith('SELECT'):
                            raise ValueError(f"SQL语句必须以SELECT开头: {table_name}")
                        
                        valid_lines += 1
            
            if valid_lines == 0:
                messagebox.showwarning("警告", "没有找到有效的配置行")
            else:
                messagebox.showinfo("成功", f"语法验证通过，找到 {valid_lines} 个有效配置")
                self._log_message(f"TAB_RULE语法验证通过，{valid_lines} 个配置")
        
        except Exception as e:
            messagebox.showerror("语法错误", f"TAB_RULE语法验证失败:\n{str(e)}")
            self._log_message(f"TAB_RULE语法验证失败: {str(e)}", "ERROR")
    
    def _format_tab_rule(self):
        """格式化TAB_RULE内容"""
        content = self.tab_rule_text.get(1.0, tk.END).strip()
        
        if not content:
            return
        
        try:
            lines = content.split('\n')
            formatted_lines = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('#') or not line:
                    formatted_lines.append(line)
                elif '=' in line:
                    table_name, sql = line.split('=', 1)
                    table_name = table_name.strip()
                    sql = sql.strip()
                    formatted_lines.append(f"{table_name} = {sql}")
                else:
                    formatted_lines.append(line)
            
            self.tab_rule_text.delete(1.0, tk.END)
            self.tab_rule_text.insert(tk.END, '\n'.join(formatted_lines))
            
            self._log_message("TAB_RULE内容已格式化")
        
        except Exception as e:
            messagebox.showerror("错误", f"格式化失败: {str(e)}")
    
    def _clear_tab_rule(self):
        """清空TAB_RULE内容"""
        if messagebox.askyesno("确认", "确定要清空TAB_RULE配置吗？"):
            self.tab_rule_text.delete(1.0, tk.END)
            self._log_message("TAB_RULE内容已清空")
    
    def _load_example_tab_rule(self):
        """加载示例TAB_RULE"""
        example_content = """# TAB_RULE配置示例
# 用户表比对
user_table = SELECT user_id, username, email, status, created_at FROM users WHERE status IN ('active', 'pending')

# 订单表比对
order_table = SELECT order_id, user_id, total_amount, order_status, created_at FROM orders WHERE created_at >= '2024-01-01'

# 产品表比对
product_table = SELECT product_id, product_name, price, category_id, is_active FROM products WHERE is_active = 1

# 带字段映射的示例
customer_table = SELECT 
    customer_id as id,
    customer_name as name,
    contact_email as email,
    registration_date as created_at
FROM customers 
WHERE registration_date >= '2023-01-01'
"""
        
        if messagebox.askyesno("确认", "确定要加载示例配置吗？这将覆盖当前内容。"):
            self.tab_rule_text.delete(1.0, tk.END)
            self.tab_rule_text.insert(tk.END, example_content)
            self._log_message("已加载TAB_RULE示例配置")
    
    def _save_config(self):
        """保存配置"""
        is_valid, error_msg = self.validate()
        if not is_valid:
            messagebox.showerror("验证失败", error_msg)
            return

        try:
            if self._save_config_file():
                self._log_message("比对规则配置保存成功")
                self._update_status("配置已保存")
                messagebox.showinfo("成功", "比对规则配置已保存到配置文件")
            else:
                messagebox.showerror("失败", "保存配置文件失败")
        except Exception as e:
            self._log_message(f"保存配置失败: {e}", "ERROR")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _load_config(self):
        """加载配置"""
        try:
            self._load_config_file()
            self._log_message("比对规则配置重新加载成功")
            self._update_status("配置已重新加载")
            messagebox.showinfo("成功", "比对规则配置已重新加载")
        except Exception as e:
            self._log_message(f"重新加载配置失败: {e}", "ERROR")
            messagebox.showerror("错误", f"重新加载配置失败: {e}")
    
    def _reset_config(self):
        """重置配置"""
        self.ignore_case_var.set(False)
        self.ignore_whitespace_var.set(True)
        self.ignore_null_var.set(False)
        self.fuzzy_match_var.set(False)
        self.numeric_precision_var.set("2")
        self.timeout_var.set("300")
        self.batch_size_var.set("1000")
        self.parallel_threads_var.set("4")
        self.datetime_format_var.set("yyyy-MM-dd HH:mm:ss")
        self.encoding_handling_var.set("自动检测")
        self.memory_optimize_var.set(True)
        
        # 重置TAB_RULE
        self._load_example_tab_rule()
        
        self._log_message("比对规则配置已重置")
        self._update_status("配置已重置")
    
    def _test_rules(self):
        """测试规则"""
        self._log_message("开始测试比对规则...")
        self._update_status("测试比对规则中...")
        
        # 模拟测试过程
        try:
            # 验证配置
            is_valid, error_msg = self.validate()
            if not is_valid:
                raise ValueError(error_msg)
            
            # 模拟测试
            messagebox.showinfo("测试结果", "比对规则测试通过！\n\n配置有效，可以开始比对任务。")
            self._log_message("比对规则测试通过")
            self._update_status("规则测试成功")
        
        except Exception as e:
            messagebox.showerror("测试失败", f"比对规则测试失败:\n{str(e)}")
            self._log_message(f"比对规则测试失败: {str(e)}", "ERROR")
            self._update_status("规则测试失败")
    
    def get_data(self):
        """获取页面数据"""
        return {
            "basic_options": {
                "ignore_case": self.ignore_case_var.get(),
                "ignore_whitespace": self.ignore_whitespace_var.get(),
                "ignore_null": self.ignore_null_var.get(),
                "fuzzy_match": self.fuzzy_match_var.get()
            },
            "data_type_rules": {
                "numeric_precision": int(self.numeric_precision_var.get()) if self.numeric_precision_var.get().isdigit() else 2,
                "datetime_format": self.datetime_format_var.get(),
                "encoding_handling": self.encoding_handling_var.get()
            },
            "performance": {
                "timeout": int(self.timeout_var.get()) if self.timeout_var.get().isdigit() else 300,
                "batch_size": int(self.batch_size_var.get()) if self.batch_size_var.get().isdigit() else 1000,
                "parallel_threads": int(self.parallel_threads_var.get()) if self.parallel_threads_var.get().isdigit() else 4,
                "memory_optimize": self.memory_optimize_var.get()
            },
            "tab_rule": self.tab_rule_text.get(1.0, tk.END).strip()
        }
    
    def set_data(self, data):
        """设置页面数据"""
        if "basic_options" in data:
            opts = data["basic_options"]
            self.ignore_case_var.set(opts.get("ignore_case", False))
            self.ignore_whitespace_var.set(opts.get("ignore_whitespace", True))
            self.ignore_null_var.set(opts.get("ignore_null", False))
            self.fuzzy_match_var.set(opts.get("fuzzy_match", False))
        
        if "data_type_rules" in data:
            rules = data["data_type_rules"]
            self.numeric_precision_var.set(str(rules.get("numeric_precision", 2)))
            self.datetime_format_var.set(rules.get("datetime_format", "yyyy-MM-dd HH:mm:ss"))
            self.encoding_handling_var.set(rules.get("encoding_handling", "自动检测"))
        
        if "performance" in data:
            perf = data["performance"]
            self.timeout_var.set(str(perf.get("timeout", 300)))
            self.batch_size_var.set(str(perf.get("batch_size", 1000)))
            self.parallel_threads_var.set(str(perf.get("parallel_threads", 4)))
            self.memory_optimize_var.set(perf.get("memory_optimize", True))
        
        if "tab_rule" in data:
            self.tab_rule_text.delete(1.0, tk.END)
            self.tab_rule_text.insert(tk.END, data["tab_rule"])
    
    def validate(self):
        """验证页面数据"""
        # 验证数值字段
        numeric_fields = [
            (self.numeric_precision_var.get(), "数值精度"),
            (self.timeout_var.get(), "超时时间"),
            (self.batch_size_var.get(), "批处理大小"),
            (self.parallel_threads_var.get(), "并行线程数")
        ]
        
        for value, name in numeric_fields:
            if not value.isdigit():
                return False, f"{name}必须是数字"
            if int(value) <= 0:
                return False, f"{name}必须大于0"
        
        # 验证TAB_RULE
        tab_rule_content = self.tab_rule_text.get(1.0, tk.END).strip()
        if not tab_rule_content:
            return False, "TAB_RULE配置不能为空"
        
        return True, ""

    def _load_config_file(self):
        """加载配置文件"""
        try:
            # 查找配置文件
            config_paths = [
                Path("../config/Config.ini"),
                Path("config/Config.ini"),
                Path("Config.ini")
            ]

            config_path = None
            for path in config_paths:
                if path.exists():
                    config_path = path
                    break

            if not config_path:
                self._log_message("未找到配置文件，使用默认配置", "WARNING")
                return

            # 读取配置文件
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')

            # 加载比对规则配置
            if 'COMPARISON_RULES' in config:
                rules_config = config['COMPARISON_RULES']

                # 加载基本选项
                self.ignore_case_var.set(rules_config.getboolean('ignore_case', False))
                self.ignore_whitespace_var.set(rules_config.getboolean('ignore_whitespace', True))
                self.ignore_null_var.set(rules_config.getboolean('ignore_null', False))
                self.fuzzy_match_var.set(rules_config.getboolean('fuzzy_match', False))

                # 加载数据类型规则
                self.numeric_precision_var.set(rules_config.get('numeric_precision', '2'))
                self.timeout_var.set(rules_config.get('timeout', '300'))
                self.batch_size_var.set(rules_config.get('batch_size', '1000'))
                self.parallel_threads_var.set(rules_config.get('parallel_threads', '4'))

                self._log_message("已加载比对规则配置")

            # 加载TAB_RULE配置
            if 'TAB_RULE' in config:
                tab_rule_content = []
                tab_rule_content.append("# TAB_RULE配置")
                tab_rule_content.append("# 格式: 表名 = SQL查询语句")
                tab_rule_content.append("")

                for key, value in config['TAB_RULE'].items():
                    tab_rule_content.append(f"{key} = {value}")

                # 更新TAB_RULE文本编辑器
                self.tab_rule_text.delete(1.0, tk.END)
                self.tab_rule_text.insert(tk.END, '\n'.join(tab_rule_content))

                self._log_message("已加载TAB_RULE配置")

            self._log_message(f"配置文件加载成功: {config_path}")

        except Exception as e:
            self._log_message(f"加载配置文件失败: {e}", "ERROR")

    def _save_config_file(self):
        """保存配置文件"""
        try:
            # 创建配置对象
            config = configparser.ConfigParser()

            # 读取现有配置（如果存在）
            config_paths = [
                Path("../config/Config.ini"),
                Path("config/Config.ini"),
                Path("Config.ini")
            ]

            config_path = None
            for path in config_paths:
                if path.exists():
                    config_path = path
                    config.read(config_path, encoding='utf-8')
                    break

            if not config_path:
                # 创建新的配置文件路径
                config_path = Path("../config/Config.ini")
                config_path.parent.mkdir(parents=True, exist_ok=True)

            # 更新比对规则配置
            if 'COMPARISON_RULES' not in config:
                config.add_section('COMPARISON_RULES')

            # 保存基本选项
            config.set('COMPARISON_RULES', 'ignore_case', str(self.ignore_case_var.get()).lower())
            config.set('COMPARISON_RULES', 'ignore_whitespace', str(self.ignore_whitespace_var.get()).lower())
            config.set('COMPARISON_RULES', 'ignore_null', str(self.ignore_null_var.get()).lower())
            config.set('COMPARISON_RULES', 'fuzzy_match', str(self.fuzzy_match_var.get()).lower())

            # 保存数据类型规则
            config.set('COMPARISON_RULES', 'numeric_precision', self.numeric_precision_var.get())
            config.set('COMPARISON_RULES', 'timeout', self.timeout_var.get())
            config.set('COMPARISON_RULES', 'batch_size', self.batch_size_var.get())
            config.set('COMPARISON_RULES', 'parallel_threads', self.parallel_threads_var.get())

            # 更新TAB_RULE配置
            if 'TAB_RULE' not in config:
                config.add_section('TAB_RULE')
            else:
                # 清空现有TAB_RULE配置
                config.remove_section('TAB_RULE')
                config.add_section('TAB_RULE')

            # 解析TAB_RULE文本内容
            tab_rule_content = self.tab_rule_text.get(1.0, tk.END).strip()
            lines = tab_rule_content.split('\n')

            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if key and value:
                        config.set('TAB_RULE', key, value)

            # 保存配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                config.write(f)

            self._log_message(f"配置文件保存成功: {config_path}")
            return True

        except Exception as e:
            self._log_message(f"保存配置文件失败: {e}", "ERROR")
            return False
