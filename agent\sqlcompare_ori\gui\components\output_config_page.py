#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出配置页面组件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from .base_page import BasePage


class OutputConfigPage(BasePage):
    """输出配置页面"""
    
    def __init__(self, parent, app_instance=None):
        self.output_format_var = tk.StringVar(value="CSV")
        self.output_path_var = tk.StringVar(value="./output/comparison_result.csv")
        self.include_matched_var = tk.BooleanVar(value=False)
        self.include_summary_var = tk.BooleanVar(value=True)
        self.auto_open_var = tk.BooleanVar(value=True)
        super().__init__(parent, app_instance)
    
    def _create_ui(self):
        """创建输出配置界面"""
        self.frame = ttk.Frame(self.parent)
        
        # 页面标题
        title_label = ttk.Label(self.frame, text="输出配置", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(20, 30))
        
        # 创建主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=40, pady=20)
        
        # 输出格式选择
        self._create_format_section(main_container)
        
        # 输出路径配置
        self._create_path_section(main_container)
        
        # 输出内容选项
        self._create_content_options_section(main_container)
        
        # 高级选项
        self._create_advanced_options_section(main_container)
        
        # 操作按钮
        self._create_action_buttons(main_container)
    
    def _create_format_section(self, parent):
        """创建输出格式选择区域"""
        format_frame = ttk.LabelFrame(parent, text="输出格式", padding=15)
        format_frame.pack(fill=tk.X, pady=(0, 20))
        
        formats = [
            ("CSV", "逗号分隔值文件 (.csv)"),
            ("Excel", "Excel工作簿 (.xlsx)"),
            ("JSON", "JSON格式文件 (.json)"),
            ("XML", "XML格式文件 (.xml)"),
            ("HTML", "HTML报告 (.html)")
        ]
        
        for i, (fmt, desc) in enumerate(formats):
            frame = ttk.Frame(format_frame)
            frame.grid(row=i//2, column=i%2, sticky=tk.W, padx=15, pady=5)
            
            ttk.Radiobutton(frame, text=fmt, variable=self.output_format_var, 
                           value=fmt, command=self._on_format_changed).pack(side=tk.LEFT)
            ttk.Label(frame, text=f"- {desc}", foreground="gray").pack(side=tk.LEFT, padx=(5, 0))
    
    def _create_path_section(self, parent):
        """创建输出路径配置区域"""
        path_frame = ttk.LabelFrame(parent, text="输出路径", padding=15)
        path_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 路径输入区域
        path_input_frame = ttk.Frame(path_frame)
        path_input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(path_input_frame, text="输出文件:").pack(side=tk.LEFT)
        
        self.path_entry = ttk.Entry(path_input_frame, textvariable=self.output_path_var, width=50)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        ttk.Button(path_input_frame, text="浏览...", 
                  command=self._browse_output_path).pack(side=tk.RIGHT)
        
        # 路径预览
        self.path_preview_label = ttk.Label(path_frame, text="", foreground="gray")
        self.path_preview_label.pack(anchor=tk.W)
        
        self._update_path_preview()
    
    def _create_content_options_section(self, parent):
        """创建输出内容选项区域"""
        content_frame = ttk.LabelFrame(parent, text="输出内容", padding=15)
        content_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 内容选项
        ttk.Checkbutton(content_frame, text="包含匹配的记录", 
                       variable=self.include_matched_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(content_frame, text="包含比对摘要", 
                       variable=self.include_summary_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(content_frame, text="完成后自动打开结果文件", 
                       variable=self.auto_open_var).pack(anchor=tk.W, pady=2)
        
        # 详细选项
        detail_frame = ttk.Frame(content_frame)
        detail_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(detail_frame, text="差异详细程度:").pack(side=tk.LEFT)
        
        self.detail_level_var = tk.StringVar(value="标准")
        detail_combo = ttk.Combobox(detail_frame, textvariable=self.detail_level_var,
                                   values=["简单", "标准", "详细"], width=15, state="readonly")
        detail_combo.pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_advanced_options_section(self, parent):
        """创建高级选项区域"""
        advanced_frame = ttk.LabelFrame(parent, text="高级选项", padding=15)
        advanced_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 文件分割选项
        split_frame = ttk.Frame(advanced_frame)
        split_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.split_files_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(split_frame, text="按表分割输出文件", 
                       variable=self.split_files_var).pack(side=tk.LEFT)
        
        # 最大记录数限制
        limit_frame = ttk.Frame(advanced_frame)
        limit_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(limit_frame, text="单文件最大记录数:").pack(side=tk.LEFT)
        
        self.max_records_var = tk.StringVar(value="10000")
        max_records_entry = ttk.Entry(limit_frame, textvariable=self.max_records_var, width=10)
        max_records_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        ttk.Label(limit_frame, text="(0表示无限制)").pack(side=tk.LEFT)
        
        # 编码选择
        encoding_frame = ttk.Frame(advanced_frame)
        encoding_frame.pack(fill=tk.X)
        
        ttk.Label(encoding_frame, text="文件编码:").pack(side=tk.LEFT)
        
        self.encoding_var = tk.StringVar(value="UTF-8")
        encoding_combo = ttk.Combobox(encoding_frame, textvariable=self.encoding_var,
                                     values=["UTF-8", "GBK", "ASCII"], width=15, state="readonly")
        encoding_combo.pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_action_buttons(self, parent):
        """创建操作按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(btn_frame, text="保存配置", 
                  command=self._save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="加载配置", 
                  command=self._load_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="重置为默认", 
                  command=self._reset_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="预览输出格式", 
                  command=self._preview_output).pack(side=tk.RIGHT)
    
    def _on_format_changed(self):
        """输出格式改变事件"""
        fmt = self.output_format_var.get()
        self._log_message(f"输出格式已切换到: {fmt}")
        
        # 更新文件扩展名
        current_path = self.output_path_var.get()
        path_obj = Path(current_path)
        
        extensions = {
            "CSV": ".csv",
            "Excel": ".xlsx",
            "JSON": ".json",
            "XML": ".xml",
            "HTML": ".html"
        }
        
        new_extension = extensions.get(fmt, ".csv")
        new_path = path_obj.with_suffix(new_extension)
        self.output_path_var.set(str(new_path))
        
        self._update_path_preview()
    
    def _browse_output_path(self):
        """浏览输出路径"""
        fmt = self.output_format_var.get()
        
        filetypes = {
            "CSV": [("CSV files", "*.csv")],
            "Excel": [("Excel files", "*.xlsx")],
            "JSON": [("JSON files", "*.json")],
            "XML": [("XML files", "*.xml")],
            "HTML": [("HTML files", "*.html")]
        }
        
        file_path = filedialog.asksaveasfilename(
            title="选择输出文件",
            defaultextension=filetypes[fmt][0][1][1:],  # 去掉*
            filetypes=filetypes[fmt] + [("All files", "*.*")]
        )
        
        if file_path:
            self.output_path_var.set(file_path)
            self._update_path_preview()
            self._log_message(f"输出路径已设置: {file_path}")
    
    def _update_path_preview(self):
        """更新路径预览"""
        path = self.output_path_var.get()
        if path:
            path_obj = Path(path)
            preview_text = f"目录: {path_obj.parent} | 文件名: {path_obj.name}"
            self.path_preview_label.config(text=preview_text)
    
    def _save_config(self):
        """保存配置"""
        is_valid, error_msg = self.validate()
        if not is_valid:
            messagebox.showerror("验证失败", error_msg)
            return
        
        config_data = self.get_data()
        self._log_message("输出配置已保存")
        self._update_status("配置已保存")
        messagebox.showinfo("成功", "输出配置已保存")
    
    def _load_config(self):
        """加载配置"""
        # 这里应该实现从配置文件加载的逻辑
        self._log_message("加载输出配置")
        self._update_status("配置已加载")
        messagebox.showinfo("成功", "输出配置已加载")
    
    def _reset_config(self):
        """重置配置"""
        self.output_format_var.set("CSV")
        self.output_path_var.set("./output/comparison_result.csv")
        self.include_matched_var.set(False)
        self.include_summary_var.set(True)
        self.auto_open_var.set(True)
        self.detail_level_var.set("标准")
        self.split_files_var.set(False)
        self.max_records_var.set("10000")
        self.encoding_var.set("UTF-8")
        
        self._update_path_preview()
        self._log_message("输出配置已重置")
        self._update_status("配置已重置")
    
    def _preview_output(self):
        """预览输出格式"""
        fmt = self.output_format_var.get()
        preview_text = f"输出格式预览: {fmt}\n\n"
        
        if fmt == "CSV":
            preview_text += "记录ID,差异类型,表名,主键值,源数据,目标数据\n"
            preview_text += "001,数据不匹配,user_table,user_001,{...},{...}\n"
        elif fmt == "JSON":
            preview_text += '{\n  "comparison_result": [\n    {\n      "record_id": "001",\n      "diff_type": "数据不匹配",\n      ...\n    }\n  ]\n}'
        elif fmt == "XML":
            preview_text += '<comparison_result>\n  <record id="001">\n    <diff_type>数据不匹配</diff_type>\n    ...\n  </record>\n</comparison_result>'
        elif fmt == "HTML":
            preview_text += '<table>\n  <tr><th>记录ID</th><th>差异类型</th>...</tr>\n  <tr><td>001</td><td>数据不匹配</td>...</tr>\n</table>'
        
        messagebox.showinfo("输出格式预览", preview_text)
    
    def get_data(self):
        """获取页面数据"""
        return {
            "output_format": self.output_format_var.get(),
            "output_path": self.output_path_var.get(),
            "include_matched": self.include_matched_var.get(),
            "include_summary": self.include_summary_var.get(),
            "auto_open": self.auto_open_var.get(),
            "detail_level": self.detail_level_var.get(),
            "split_files": self.split_files_var.get(),
            "max_records": int(self.max_records_var.get()) if self.max_records_var.get().isdigit() else 0,
            "encoding": self.encoding_var.get()
        }
    
    def set_data(self, data):
        """设置页面数据"""
        if "output_format" in data:
            self.output_format_var.set(data["output_format"])
        if "output_path" in data:
            self.output_path_var.set(data["output_path"])
        if "include_matched" in data:
            self.include_matched_var.set(data["include_matched"])
        if "include_summary" in data:
            self.include_summary_var.set(data["include_summary"])
        if "auto_open" in data:
            self.auto_open_var.set(data["auto_open"])
        if "detail_level" in data:
            self.detail_level_var.set(data["detail_level"])
        if "split_files" in data:
            self.split_files_var.set(data["split_files"])
        if "max_records" in data:
            self.max_records_var.set(str(data["max_records"]))
        if "encoding" in data:
            self.encoding_var.set(data["encoding"])
        
        self._update_path_preview()
    
    def validate(self):
        """验证页面数据"""
        # 验证输出路径
        path = self.output_path_var.get()
        if not path:
            return False, "输出路径不能为空"
        
        # 验证路径目录是否存在
        path_obj = Path(path)
        if not path_obj.parent.exists():
            try:
                path_obj.parent.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                return False, f"无法创建输出目录: {str(e)}"
        
        # 验证最大记录数
        max_records = self.max_records_var.get()
        if max_records and not max_records.isdigit():
            return False, "最大记录数必须是数字"
        
        return True, ""
