#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL数据库连接器
基于psycopg2驱动，支持高性能数据读取和连接池管理
"""

import logging
import time
from typing import Dict, Any, Iterator, List, Optional
import threading

try:
    import psycopg2
    import psycopg2.extras
    from psycopg2 import pool
except ImportError:
    psycopg2 = None

from .base_connector import BaseConnector

logger = logging.getLogger(__name__)


class PostgreSQLConnectionPool:
    """PostgreSQL连接池管理器"""
    
    def __init__(self):
        self._pools = {}
        self._lock = threading.Lock()
    
    def get_connection(self, db_config: Dict[str, Any]) -> Any:
        """从连接池获取连接"""
        key = self._get_connection_key(db_config)
        
        with self._lock:
            if key not in self._pools:
                self._pools[key] = self._create_connection_pool(db_config)
        
        try:
            return self._pools[key].getconn()
        except Exception as e:
            logger.error(f"从连接池获取PostgreSQL连接失败: {e}")
            raise
    
    def return_connection(self, db_config: Dict[str, Any], conn: Any):
        """将连接返回到连接池"""
        key = self._get_connection_key(db_config)
        
        if key in self._pools:
            try:
                self._pools[key].putconn(conn)
            except Exception as e:
                logger.warning(f"返回PostgreSQL连接到池失败: {e}")
    
    def _get_connection_key(self, db_config: Dict[str, Any]) -> str:
        """生成连接池键"""
        return f"{db_config.get('host', 'localhost')}:{db_config.get('port', 5432)}:{db_config.get('database', '')}"
    
    def _create_connection_pool(self, db_config: Dict[str, Any]):
        """创建连接池"""
        try:
            return psycopg2.pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=5,
                host=db_config.get('host', 'localhost'),
                port=db_config.get('port', 5432),
                database=db_config.get('database', ''),
                user=db_config.get('username', ''),
                password=db_config.get('password', ''),
                connect_timeout=30,
                application_name='SQLCompare-PostgreSQL'
            )
        except Exception as e:
            logger.error(f"创建PostgreSQL连接池失败: {e}")
            raise


class PostgreSQLConnector(BaseConnector):
    """
    PostgreSQL数据库连接器
    
    特性：
    - 支持连接池管理
    - 批量数据读取优化
    - 自动重连机制
    - 性能统计
    """
    
    _connection_pool = PostgreSQLConnectionPool()
    _driver_configured = False
    _config_lock = threading.Lock()
    
    def __init__(self, db_config: Dict[str, Any], query: str = "", batch_size: int = 10000):
        """
        初始化PostgreSQL连接器
        
        Args:
            db_config: 数据库配置字典
            query: SQL查询语句
            batch_size: 批量读取大小
        """
        super().__init__(batch_size)
        
        # 检查驱动可用性
        if psycopg2 is None:
            raise ImportError("PostgreSQL驱动未安装，请运行: pip install psycopg2-binary")
        
        self.db_config = db_config
        self.query = query
        self.conn = None
        self.cursor = None
        self._use_connection_pool = True
        
        # 性能统计
        self._stats = {
            'connection_time': 0.0,
            'query_time': 0.0,
            'fetch_time': 0.0,
            'total_fetched': 0
        }
        
        logger.info(f"PostgreSQL连接器初始化完成，批量大小: {batch_size}")
    
    def connect(self):
        """建立到PostgreSQL数据库的连接"""
        start_time = time.perf_counter()
        
        try:
            if self._use_connection_pool:
                self.conn = self._connection_pool.get_connection(self.db_config)
            else:
                self.conn = self._create_direct_connection()
            
            self._stats['connection_time'] += time.perf_counter() - start_time
            logger.info("PostgreSQL数据库连接成功")
            
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {e}")
            raise
    
    def _create_direct_connection(self):
        """创建直接连接（不使用连接池）"""
        try:
            return psycopg2.connect(
                host=self.db_config.get('host', 'localhost'),
                port=self.db_config.get('port', 5432),
                database=self.db_config.get('database', ''),
                user=self.db_config.get('username', ''),
                password=self.db_config.get('password', ''),
                connect_timeout=30,
                application_name='SQLCompare-PostgreSQL'
            )
        except Exception as e:
            logger.error(f"创建PostgreSQL直接连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
            
            if self.conn:
                if self._use_connection_pool:
                    self._connection_pool.return_connection(self.db_config, self.conn)
                else:
                    self.conn.close()
                self.conn = None
            
            logger.info("PostgreSQL连接已断开")
            
        except Exception as e:
            logger.warning(f"断开PostgreSQL连接时出现警告: {e}")
    
    def fetch_data(self, query: str = None) -> Iterator[Dict[str, Any]]:
        """
        获取数据的生成器
        
        Args:
            query: SQL查询语句，如果为None则使用初始化时的查询
            
        Yields:
            Dict[str, Any]: 数据行字典
        """
        if not self.conn:
            self.connect()
        
        query_to_use = query or self.query
        if not query_to_use:
            raise ValueError("未提供查询语句")
        
        start_time = time.perf_counter()
        
        try:
            # 使用字典游标以获得更好的数据访问
            self.cursor = self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            
            logger.info(f"执行PostgreSQL查询: {query_to_use[:100]}...")
            self.cursor.execute(query_to_use)
            
            self._stats['query_time'] += time.perf_counter() - start_time
            
            # 批量获取数据
            fetch_start = time.perf_counter()
            
            while True:
                rows = self.cursor.fetchmany(self.batch_size)
                if not rows:
                    break
                
                self._stats['total_fetched'] += len(rows)
                
                for row in rows:
                    yield dict(row)
            
            self._stats['fetch_time'] += time.perf_counter() - fetch_start
            
            logger.info(f"PostgreSQL数据获取完成，共获取 {self._stats['total_fetched']} 条记录")
            
        except Exception as e:
            logger.error(f"PostgreSQL数据获取失败: {e}")
            raise
        finally:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
    
    def get_database_version(self) -> str:
        """获取数据库版本信息"""
        if not self.conn:
            self.connect()
        
        try:
            with self.conn.cursor() as cursor:
                cursor.execute("SELECT version()")
                version = cursor.fetchone()[0]
                return version
        except Exception as e:
            logger.warning(f"获取PostgreSQL版本失败: {e}")
            return "Unknown"
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            if not self.conn:
                self.connect()
            
            with self.conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
                
        except Exception as e:
            logger.error(f"PostgreSQL连接测试失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'connector_type': 'PostgreSQL',
            'connection_time': round(self._stats['connection_time'], 3),
            'query_time': round(self._stats['query_time'], 3),
            'fetch_time': round(self._stats['fetch_time'], 3),
            'total_fetched': self._stats['total_fetched'],
            'batch_size': self.batch_size,
            'use_connection_pool': self._use_connection_pool
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
    
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        try:
            self.disconnect()
        except:
            pass
