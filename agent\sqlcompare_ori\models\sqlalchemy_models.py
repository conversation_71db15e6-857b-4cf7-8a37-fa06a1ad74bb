"""
SQLAlchemy数据模型层
本模块专注于数据库模型定义，实现关注点分离的架构设计。
包含所有核心业务表的SQLAlchemy模型定义，不包含业务逻辑和API接口定义。

职责:
1. 定义所有数据库表结构
2. 定义表之间的关系映射
3. 定义索引和约束
4. 提供数据库连接和会话管理工具

设计原则:
- 单一职责: 只关注数据结构定义
- 无业务逻辑: 不包含业务操作方法
- 完整映射: 完整映射数据库表结构
- 关系明确: 清晰定义表间关系
"""
import uuid
import logging
from enum import Enum
from urllib.parse import quote_plus
from typing import Dict, Any, Optional

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, JSON, Boolean,
    ForeignKey, Index, DECIMAL, create_engine, UniqueConstraint, text
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.sql import func

logger = logging.getLogger(__name__)

# 创建基础模型
Base = declarative_base()

# 枚举定义
class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ComparisonType(str, Enum):
    """比对类型枚举"""
    CONTENT = "content"
    STRUCTURE = "structure"

class DatabaseType(str, Enum):
    """数据库类型枚举"""
    DB2 = "db2"
    MYSQL = "mysql"
    ORACLE = "oracle"
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"

class DifferenceStatus(str, Enum):
    """差异状态枚举"""
    IDENTICAL = "ID"      # 记录完全相同
    DIFFERENT = "DF"      # 记录存在差异
    SOURCE_ONLY = "SO"    # 仅在源数据库中存在
    TARGET_ONLY = "TO"    # 仅在目标数据库中存在
    FIELD_DIFF = "FD"     # 字段级差异

class DifferenceType(str, Enum):
    """差异类型枚举"""
    VALUE_DIFF = "VD"    # 值不同
    TYPE_DIFF = "TD"     # 类型不同
    FORMAT_DIFF = "FD"   # 格式不同
    MISSING = "MS"       # 缺失
    EXTRA = "EX"         # 多余

class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARN = "WARN"
    ERROR = "ERROR"

# SQLAlchemy模型定义

class User(Base):
    """用户表"""
    __tablename__ = "user_agent"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(50), unique=True, nullable=False, index=True)
    username = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, index=True)
    password = Column(String(255), nullable=False)
    role = Column(String(20), default=UserRole.USER.value, index=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    last_login_at = Column(DateTime)
    
    # 关系
    tasks = relationship("ComparisonTask", back_populates="user")
    sessions = relationship("UserSession", back_populates="user")

class UserSession(Base):
    """用户会话表"""
    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(128), unique=True, nullable=False, index=True)
    user_id = Column(String(50), ForeignKey("user_agent.user_id", ondelete="CASCADE"), nullable=False, index=True)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    expires_at = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, server_default=func.now())

    # 关系
    user = relationship("User", back_populates="sessions")

class ComparisonConnection(Base):
    """数据库连接表 - 管理所有数据库连接配置"""
    __tablename__ = "comparison_connections"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)                    # 连接名称
    type = Column(String(20), nullable=False)                     # 数据库类型
    host = Column(String(255), nullable=False)                    # 主机地址
    port = Column(Integer, nullable=False)                        # 端口号
    username = Column(String(100), nullable=False)                # 用户名
    password = Column(String(255), nullable=False)                # 密码（加密存储）
    database = Column(String(100), nullable=False)                # 数据库名
    params = Column(JSON)                                         # 额外参数
    status = Column(String(20), default='active')                 # 状态：active, inactive
    create_time = Column(DateTime, server_default=func.now())
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # 关系 - 添加级联删除策略
    source_models = relationship("ComparisonModel", foreign_keys="ComparisonModel.source_connid", back_populates="source_conn", cascade="all, delete-orphan")
    target_models = relationship("ComparisonModel", foreign_keys="ComparisonModel.target_connid", back_populates="target_conn", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_connections_name', 'name'),
        Index('idx_connections_type', 'type'),
        Index('idx_connections_status', 'status'),
    )

    def to_dict(self) -> Dict[str, Any]:
        """将连接对象转换为字典格式"""
        return {
            'TYPE': self.type.upper(),
            'IP': self.host,
            'PORT': str(self.port),
            'USER_NAME': self.username,
            'PASSWORD': self.password,
            'SCHEMA': self.database
        }

class ComparisonModel(Base):
    """比对模型表 - 定义比对计划和配置"""
    __tablename__ = "comparison_models"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)       # 模型名称
    description = Column(Text)                                    # 模型描述
    source_connid = Column(Integer, ForeignKey("comparison_connections.id", ondelete="SET NULL"), nullable=True)  # 源连接ID
    target_connid = Column(Integer, ForeignKey("comparison_connections.id", ondelete="SET NULL"), nullable=True)  # 目标连接ID
    cmp_type = Column(String(20), default='content')             # 比对类型
    global_config = Column(JSON)                                 # 全局配置参数
    status = Column(Boolean, default=True)                       # 是否启用
    create_time = Column(DateTime, server_default=func.now())
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # 关系 - 添加级联删除策略
    source_conn = relationship("ComparisonConnection", foreign_keys=[source_connid], back_populates="source_models")
    target_conn = relationship("ComparisonConnection", foreign_keys=[target_connid], back_populates="target_models")
    table_rules = relationship("ComparisonTableRule", back_populates="model", cascade="all, delete-orphan")
    tasks = relationship("ComparisonTask", back_populates="model", cascade="all, delete-orphan")

    # 索引和约束
    __table_args__ = (
        UniqueConstraint('name', name='uq_model_name'),              # 模型名称唯一约束
        Index('idx_model_name', 'name'),
        Index('idx_model_status', 'status'),
        Index('idx_model_source_conn', 'source_connid'),
        Index('idx_model_target_conn', 'target_connid'),
    )

    def to_config_format(self) -> Dict[str, Any]:
        """转换为SmartConfigManager兼容格式"""
        return {
            'DB1': {
                'ip': self.source_conn.host,
                'port': str(self.source_conn.port),
                'user_name': self.source_conn.username,
                'password': self.source_conn.password,
                'database': self.source_conn.database,
                'type': self.source_conn.type.upper(),
                'schema': self.source_conn.params.get('schema', '') if self.source_conn.params else '',
            },
            'DB2': {
                'ip': self.target_conn.host,
                'port': str(self.target_conn.port),
                'user_name': self.target_conn.username,
                'password': self.target_conn.password,
                'database': self.target_conn.database,
                'type': self.target_conn.type.upper(),
                'schema': self.target_conn.params.get('schema', '') if self.target_conn.params else '',
            },
            'COMMON': {
                'CMP_TYPE': '2' if self.cmp_type == 'content' else '1',
                'TITLE': self.name,
                **(self.global_config if self.global_config else {})
            }
        }

class ComparisonTableRule(Base):
    """数据库比对规则表 - 存储每个表的比对SQL规则"""
    __tablename__ = "comparison_table_rules"

    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("comparison_models.id", ondelete="CASCADE"), nullable=False, index=True)  # 关联比对模型
    table_id = Column(String(50), nullable=False)                 # 表标识
    table_name = Column(String(255), nullable=False)              # 实际表名
    remark = Column(String(500))                                  # 备注说明
    sql_1 = Column(Text, nullable=False)                          # 源端SQL
    sql_2 = Column(Text, nullable=False)                          # 目标端SQL

    # 扩展配置
    primary_keys = Column(JSON)                                   # 主键字段列表
    ignore_fields = Column(JSON)                                  # 忽略字段列表
    field_mappings = Column(JSON)                                 # 字段映射关系

    # 状态管理
    is_active = Column(Boolean, default=True)                     # 是否启用
    create_time = Column(DateTime, server_default=func.now())
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # 关系 - 更新关系映射，添加级联删除策略
    model = relationship("ComparisonModel", back_populates="table_rules")

    __table_args__ = (
        Index('idx_rule_model_id', 'model_id'),
        Index('idx_rule_table_id', 'table_id'),
        Index('idx_rule_model_table', 'model_id', 'table_id'),
        Index('idx_rule_active', 'is_active'),
        UniqueConstraint('model_id', 'table_id', name='uq_model_table'), 
    )

    def to_dict(self) -> Dict[str, Any]:
        """将表规则对象转换为字典格式"""
        return {
            'table_id': self.table_id,
            'table_name': self.table_name,
            'sql_1': self.sql_1,
            'sql_2': self.sql_2,
            'primary_keys': self.primary_keys,
            'ignore_fields': self.ignore_fields,
            'field_mappings': self.field_mappings
        }

    def to_xml_format(self) -> str:
        """转换为XML规则格式，支持与现有XML规则文件兼容"""
        return f'''<table table_id="{self.table_id}" remark="{self.remark or ''}">
    <sql_1>{self.sql_1}</sql_1>
    <sql_2>{self.sql_2}</sql_2>
</table>'''

# 执行管理层数据模型
class ComparisonTask(Base):
    """比对任务表"""
    __tablename__ = "comparison_tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(50), unique=True, nullable=False, index=True)
    task_name = Column(String(200))                               # 任务名称

    user_id = Column(String(50), ForeignKey("user_agent.user_id", ondelete="CASCADE"), nullable=False, index=True)
    model_id = Column(Integer, ForeignKey("comparison_models.id", ondelete="CASCADE"), nullable=True, index=True)  # 支持TaskCreateDirect场景

    # 执行状态和进度信息
    status = Column(String(20), default=TaskStatus.PENDING.value, index=True)
    progress_pct = Column(DECIMAL(5,2), default=0.0)             # 执行进度
    current_step = Column(String(100))                           # 当前执行步骤

    # 时间戳信息
    create_time = Column(DateTime, server_default=func.now(), index=True)
    start_time = Column(DateTime)
    complete_time = Column(DateTime)
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # 统计信息
    total_records = Column(Integer, default=0)                   # 总记录数
    processed_records = Column(Integer, default=0)               # 已处理记录数
    diff_records = Column(Integer, default=0)                    # 差异记录数
    source_only = Column(Integer, default=0)                     # 仅源端记录数
    target_only = Column(Integer, default=0)                     # 仅目标端记录数
    exec_time = Column(DECIMAL(10,3), default=0)                 # 执行时间（秒）

    # 错误信息
    error_msg = Column(Text)                                     # 错误消息
    error_details = Column(JSON)                                 # 详细错误信息

    # 数据清理机制支持
    retention_days = Column(Integer, default=30)                 # 数据保留天数
    auto_cleanup = Column(Boolean, default=True)                 # 是否自动清理

    # 关系映射 - 添加级联删除策略
    user = relationship("User", back_populates="tasks")
    model = relationship("ComparisonModel", back_populates="tasks")
    results = relationship("ComparisonResult", back_populates="task", cascade="all, delete-orphan")

    # 复合索引
    __table_args__ = (
        Index('idx_tasks_task_id', 'task_id'),                    # 任务ID索引
        Index('idx_tasks_user_status', 'user_id', 'status'),      # 用户状态索引
        Index('idx_tasks_model_id', 'model_id'),                  # 模型ID索引
        Index('idx_tasks_status', 'status'),                      # 状态索引
        Index('idx_tasks_task_status', 'task_id', 'status'),      # 任务状态复合索引
        Index('idx_tasks_model_status', 'model_id', 'status'),    # 模型状态复合索引
        Index('idx_tasks_create_time', 'create_time'),            # 创建时间索引
    )

    # 任务汇总查询方法
    @classmethod
    def get_task_summary(cls, session, task_id: str):
        """
        获取任务汇总信息（直接从任务表获取，无需单独的汇总表）

        Args:
            session: SQLAlchemy会话
            task_id: 任务ID

        Returns:
            dict: 任务汇总信息
        """
        # 查询任务记录（单表设计，每个任务只有一条记录）
        task = session.query(cls).filter_by(task_id=task_id).first()

        if not task:
            return None

        # 计算匹配记录数
        matched_records = (task.total_records or 0) - (task.diff_records or 0) - (task.source_only or 0) - (task.target_only or 0)

        return {
            # 任务基本信息
            'task_id': task_id,
            'task_name': task.task_name,
            'description': task.description,
            'user_id': task.user_id,
            'model_id': task.model_id,
            'create_time': task.create_time,
            'start_time': task.start_time,
            'complete_time': task.complete_time,
            'update_time': task.update_time,

            # 状态信息
            'status': task.status,
            'progress_pct': task.progress_pct,
            'current_step': task.current_step,

            # 数据统计（包含计算的匹配记录数）
            'total_records': task.total_records or 0,
            'processed_records': task.processed_records or 0,
            'matched_records': max(0, matched_records),  # 确保不为负数
            'diff_records': task.diff_records or 0,
            'source_only': task.source_only or 0,
            'target_only': task.target_only or 0,
            'exec_time': task.exec_time or 0,

            # 错误信息
            'error_msg': task.error_msg,
            'error_details': task.error_details
        }

    @classmethod
    def get_task_executions(cls, session, task_id: str):
        """
        获取任务的所有执行记录

        Args:
            session: SQLAlchemy会话
            task_id: 任务ID

        Returns:
            List[ComparisonTask]: 执行记录列表
        """
        return session.query(cls).filter_by(task_id=task_id).order_by(cls.create_time).all()


class ComparisonResult(Base):
    """比对结果详情表"""
    __tablename__ = "comparison_results"

    id = Column(Integer, primary_key=True, index=True)

    # 关联信息
    task_id = Column(String(50), ForeignKey("comparison_tasks.task_id", ondelete="CASCADE"), nullable=False, index=True)
    table_name = Column(String(100), nullable=False)  # 表名
    record_key = Column(String(128), nullable=False)  # 记录键
    
    # 差异信息
    status = Column(String(2), nullable=False, index=True)
    field_name = Column(String(100))
    source_value = Column(Text)
    target_value = Column(Text)

    # 扩展信息
    diff_type = Column(String(2))
    # 分区策略
    partition_key = Column(String(50), index=True)
    
    # 关系
    task = relationship("ComparisonTask", back_populates="results")

    # 复合索引
    __table_args__ = (
        Index('idx_results_table_status', 'table_name', 'status'),
        Index('idx_results_table_key', 'table_name', 'record_key'),
        Index('idx_results_task_status', 'task_id', 'status'),
        Index('idx_results_task_table', 'task_id', 'table_name'),
        Index('idx_results_partition_status', 'partition_key', 'status'),
    )

class ApiAccessLog(Base):
    """API访问日志表"""
    __tablename__ = "api_access_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(String(50), unique=True, nullable=False)
    user_id = Column(String(50), index=True)
    endpoint = Column(String(200), nullable=False, index=True)
    method = Column(String(10), nullable=False)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    request_body = Column(Text)
    response_status = Column(Integer, index=True)
    response_time_ms = Column(Integer)
    created_at = Column(DateTime, server_default=func.now(), index=True)

def build_conn_engine(db_type: str, host: str, port: int, username: str, password: str, database: str) -> Optional[str]:
    """构建数据库连接URL"""
    try:
        # URL编码密码中的特殊字符
        encoded_password = quote_plus(password) if password else ""
        encoded_username = quote_plus(username) if username else ""

        connection_url = None
        db_type_lower = db_type.lower()

        if db_type_lower == 'mysql':
            # MySQL连接URL - 优先使用pymysql驱动
            connection_url = f"mysql+pymysql://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
        elif db_type_lower == 'db2':
            # DB2连接URL - 使用ibm_db驱动
            connection_url = f"db2+ibm_db://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
        elif db_type_lower in ['postgresql', 'postgres']:
            # PostgreSQL连接URL - 使用psycopg2驱动
            connection_url = f"postgresql+psycopg2://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
        elif db_type_lower == 'sqlite':
            # SQLite连接URL - 文件路径
            connection_url = f"sqlite:///{database}"
        elif db_type_lower in ['oracle', 'ora']:
            # Oracle连接URL - 使用cx_oracle驱动
            connection_url = f"oracle+cx_oracle://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
        elif db_type_lower in ['sqlserver', 'mssql']:
            # SQL Server连接URL - 使用pyodbc驱动
            connection_url = f"mssql+pyodbc://{encoded_username}:{encoded_password}@{host}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server"
        else:
            return None
        
        # 创建数据库引擎
        return create_engine(connection_url)

    except Exception as e:
        logger.error(f"创建数据库引擎失败: {e}")
        return None

# 数据库连接和会话管理
def create_database_engine(database_url: str, **kwargs):
    """创建数据库引擎"""
    default_config = {'echo': False, 'pool_size': 2}
    engine_config = {**default_config, **kwargs}
    engine = create_engine(database_url, **engine_config)

    try:
        with engine.connect() as conn:
            if database_url.startswith('sqlite'):
                conn.execute(text("PRAGMA journal_mode=MEMORY"))     # 内存模式，最快但不安全
                conn.execute(text("PRAGMA synchronous=OFF"))         # 关闭同步，最大性能
                conn.execute(text("PRAGMA cache_size=100000"))       # 大幅增大缓存
                conn.execute(text("PRAGMA temp_store=MEMORY"))       # 临时数据存储在内存
                conn.execute(text("PRAGMA locking_mode=EXCLUSIVE"))  # 独占锁模式
                conn.execute(text("PRAGMA count_changes=OFF"))       # 关闭计数
                conn.execute(text("PRAGMA auto_vacuum=NONE"))        # 关闭自动清理

            elif database_url.startswith('mysql'):
                conn.execute(text("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"))
                conn.execute(text("SET SESSION time_zone = '+00:00'"))
                conn.execute(text("SET SESSION foreign_key_checks = 1"))

            elif database_url.startswith('postgresql'):
                conn.execute(text("SET synchronous_commit = off"))  # 异步提交，提升性能
                conn.execute(text("SET random_page_cost = 1.1"))    # SSD优化
                conn.execute(text("SET effective_cache_size = '256MB'"))  # 缓存大小
                conn.execute(text("SET work_mem = '4MB'"))          # 工作内存
                conn.execute(text("SET maintenance_work_mem = '64MB'"))  # 维护工作内存

            conn.commit()

    except Exception as e:
        logger.warning(f"数据库特定设置初始化失败: {e}")

    return engine

def create_tables(engine):
    """创建所有表"""
    try:
        Base.metadata.create_all(bind=engine)

    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise

def get_session_factory(engine):
    """获取会话工厂"""
    return sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 导出接口
__all__ = [
    'UserRole', 'TaskStatus', 'ComparisonType', 'DifferenceStatus', 'DifferenceType', 'DatabaseType',
    'User', 'UserSession', 'ComparisonConnection', 'ComparisonModel',
    'ComparisonTableRule', 'ComparisonTask', 'ComparisonResult', 'ApiAccessLog',
    'build_conn_engine', 'create_database_engine', 'create_tables', 'get_session_factory'
]
