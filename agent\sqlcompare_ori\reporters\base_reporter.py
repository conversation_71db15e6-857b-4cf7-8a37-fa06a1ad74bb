# reporters/base_reporter.py
from abc import ABC, abstractmethod
from core.models import DiffResult

class BaseReporter(ABC):
    """所有差异报告器的抽象基类。"""

    def __init__(self, config=None):
        """
        基础报告器初始化

        Args:
            config: 配置参数（可选）
        """
        self.config = config or {}

    @abstractmethod
    def open(self):
        """打开报告目标（例如，文件）。"""
        pass

    @abstractmethod
    def close(self):
        """关闭报告目标。"""
        pass

    @abstractmethod
    def report_diff(self, diff_result: DiffResult):
        """记录一条差异。"""
        pass

    def __enter__(self):
        self.open()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()