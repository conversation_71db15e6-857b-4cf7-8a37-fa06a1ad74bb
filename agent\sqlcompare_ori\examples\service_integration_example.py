"""
服务化集成示例
展示如何将现有SQLite比对功能集成到服务化架构中
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from models.sqlalchemy_models import (
    Base, User, ComparisonTask, TaskStatus, ComparisonType,
    create_database_engine, create_tables, get_session_factory
)
from adapters.service_sqlite_adapter import ServiceSqliteAdapter, create_service_task
from core.engine import compare_sources_memory_dict
from connectors.db2_connector import DB2Connector


class ComparisonService:
    """比对服务类"""
    
    def __init__(self, database_url: str = "sqlite:///service_comparison.db"):
        self.engine = create_database_engine(database_url)
        create_tables(self.engine)
        self.SessionLocal = get_session_factory(self.engine)
        
    def create_user(self, user_data: Dict[str, Any]) -> str:
        """创建用户"""
        with self.SessionLocal() as session:
            user = User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password'],
                role=user_data.get('role', 'user')
            )
            session.add(user)
            session.commit()
            return user.user_id
    
    def create_comparison_task(self, user_id: str, task_data: Dict[str, Any]) -> str:
        """创建比对任务"""
        with self.SessionLocal() as session:
            task_id = create_service_task(session, user_id, task_data)
            return task_id
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        with self.SessionLocal() as session:
            task = session.query(ComparisonTask).filter(
                ComparisonTask.task_id == task_id
            ).first()
            
            if not task:
                return {"error": "Task not found"}
            
            return {
                "task_id": task.task_id,
                "status": task.status,
                "progress_percentage": float(task.progress_percentage),
                "current_step": task.current_step,
                "processed_records": task.processed_records,
                "total_records": task.total_records,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message
            }
    
    def get_comparison_results(self, task_id: str, page: int = 1, page_size: int = 100,
                             status_filter: str = None) -> Dict[str, Any]:
        """获取比对结果"""
        with self.SessionLocal() as session:
            from models.sqlalchemy_models import ComparisonResult
            
            query = session.query(ComparisonResult).filter(
                ComparisonResult.task_id == task_id
            )
            
            if status_filter:
                query = query.filter(ComparisonResult.status == status_filter)
            
            total = query.count()
            offset = (page - 1) * page_size
            results = query.offset(offset).limit(page_size).all()
            
            return {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size,
                "results": [
                    {
                        "id": r.id,
                        "record_key": r.record_key,
                        "status": r.status,
                        "field_name": r.field_name,
                        "value_a": r.value_a,
                        "value_b": r.value_b,
                        "severity": r.severity,
                        "created_at": r.created_at.isoformat()
                    }
                    for r in results
                ]
            }
    
    async def execute_comparison_task(self, task_id: str) -> bool:
        """执行比对任务"""
        try:
            with self.SessionLocal() as session:
                # 创建适配器
                adapter = ServiceSqliteAdapter(session, task_id)
                
                # 获取任务配置
                task = session.query(ComparisonTask).filter(
                    ComparisonTask.task_id == task_id
                ).first()
                
                if not task:
                    raise ValueError(f"Task {task_id} not found")
                
                # 初始化任务
                adapter.initialize_task({
                    "source_conn": task.source_conn,
                    "target_conn": task.target_conn,
                    "table_name": task.table_name
                })
                
                # 更新进度
                adapter.update_progress("准备数据源连接", 10.0)
                
                # 创建数据源连接
                source_connector = DB2Connector(task.source_conn)
                target_connector = DB2Connector(task.target_conn)
                
                # 创建增强的SQLite报告器
                sqlite_config = {
                    'db_path': f'task_{task_id}_results.db',
                    'table_name': 'comparison_results',
                    'comparison_table': task.table_name,
                    'batch_size': task.batch_size,
                    'silent_mode': True,
                    'append_mode': False
                }
                
                sqlite_reporter = adapter.create_sqlite_reporter(sqlite_config)
                
                # 更新进度
                adapter.update_progress("开始数据比对", 20.0)
                
                # 执行比对
                try:
                    with source_connector, target_connector, sqlite_reporter:
                        compare_sources_memory_dict(
                            source_connector, 
                            target_connector, 
                            sqlite_reporter
                        )
                    
                    # 完成任务
                    summary_data = {
                        'total_records': sqlite_reporter.total_records,
                        'different_records': sqlite_reporter.total_records,  # 简化示例
                        'matched_records': 0,
                        'source_only_records': 0,
                        'target_only_records': 0,
                        'source_info': task.source_conn,
                        'target_info': task.target_conn,
                        'comparison_config': {
                            'table_name': task.table_name,
                            'batch_size': task.batch_size
                        }
                    }
                    
                    adapter.complete_task(summary_data)
                    return True
                    
                except Exception as e:
                    adapter.fail_task(str(e), {"exception_type": type(e).__name__})
                    return False
                    
        except Exception as e:
            print(f"执行任务失败: {e}")
            return False


# 使用示例
async def main():
    """主函数示例"""
    # 创建服务实例
    service = ComparisonService()
    
    # 创建用户
    user_data = {
        'user_id': 'demo_user',
        'username': '演示用户',
        'email': '<EMAIL>',
        'password': 'demo_hash',
        'role': 'user'
    }
    
    try:
        user_id = service.create_user(user_data)
        print(f"创建用户成功: {user_id}")
    except Exception as e:
        print(f"用户可能已存在: {e}")
        user_id = 'demo_user'
    
    # 创建比对任务
    task_data = {
        'task_name': '演示比对任务',
        'description': '这是一个演示任务',
        'comparison_type': ComparisonType.CONTENT.value,
        'source_conn': {
            'host': 'localhost',
            'port': 50000,
            'database': 'source_db',
            'username': 'user',
            'password': 'pass'
        },
        'target_conn': {
            'host': 'localhost',
            'port': 50000,
            'database': 'target_db',
            'username': 'user',
            'password': 'pass'
        },
        'table_name': 'demo_table',
        'batch_size': 10000,
        'priority': 5
    }
    
    task_id = service.create_comparison_task(user_id, task_data)
    print(f"创建任务成功: {task_id}")
    
    # 查询任务状态
    status = service.get_task_status(task_id)
    print(f"任务状态: {status}")
    
    # 模拟执行任务（实际应该在后台异步执行）
    print("开始执行比对任务...")
    # success = await service.execute_comparison_task(task_id)
    # print(f"任务执行结果: {'成功' if success else '失败'}")
    
    # 查询比对结果
    # results = service.get_comparison_results(task_id, page=1, page_size=10)
    # print(f"比对结果: {results}")


if __name__ == "__main__":
    asyncio.run(main())
