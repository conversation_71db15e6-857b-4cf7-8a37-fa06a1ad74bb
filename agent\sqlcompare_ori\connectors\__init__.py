"""
数据库连接器模块

提供各种数据库的连接器实现，支持：
- DB2
- MySQL
- Oracle
- PostgreSQL
- GaussDB
"""

from .base_connector import BaseConnector
from .db2_connector import DB2Connector
from .mysql_connector import MySqlConnector
from .oracle_connector import OracleConnector
from .postgresql_connector import PostgreSQLConnector
from .gaussdb_connector import GaussDBConnector

__all__ = [
    'BaseConnector',
    'DB2Connector',
    'MySqlConnector',
    'OracleConnector',
    'PostgreSQLConnector',
    'GaussDBConnector'
]