#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证API端点
"""

import os
import sys
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from backend.core.database import get_db
from backend.models.auth_models import (
    UserCreate, UserUpdate, UserResponse,
    LoginRequest, LoginResponse, TokenResponse,
    PasswordChangeRequest, PasswordResetRequest, PasswordResetConfirm,
    UserPermissionsResponse, APIKeyCreate, APIKeyResponse
)
from backend.models.base import APIResponse, PaginationParams, ListResponse
from backend.services.auth_service import AuthService
from backend.core.exceptions import UnauthorizedError

router = APIRouter()
security = HTTPBearer()


def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    """获取认证服务实例"""
    return AuthService(db)


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
):
    """获取当前用户"""
    try:
        user = auth_service.get_current_user(credentials.credentials)
        if not user:
            raise UnauthorizedError("无效的访问令牌")
        return user
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/register", response_model=APIResponse)
async def register(
    user_data: UserCreate,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    用户注册
    
    - **username**: 用户名
    - **email**: 邮箱地址
    - **password**: 密码
    - **full_name**: 全名
    - **role**: 用户角色
    """
    try:
        user = await auth_service.create_user(user_data)
        return APIResponse.success_response(
            data={"user_id": user.id},
            message="用户注册成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    用户登录
    
    - **username**: 用户名
    - **password**: 密码
    - **remember_me**: 记住我
    """
    try:
        login_result = await auth_service.authenticate_user(
            login_data.username, 
            login_data.password,
            login_data.remember_me
        )
        if not login_result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        return login_result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/logout", response_model=APIResponse)
async def logout(
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """用户登出"""
    try:
        await auth_service.logout_user(current_user["id"])
        return APIResponse.success_response(message="登出成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """刷新访问令牌"""
    try:
        token_result = await auth_service.refresh_token(current_user["id"])
        return token_result
    except Exception as e:
        raise HTTPException(status_code=401, detail=str(e))


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: dict = Depends(get_current_user)
):
    """获取当前用户信息"""
    return UserResponse(**current_user)


@router.put("/me", response_model=APIResponse)
async def update_current_user(
    user_data: UserUpdate,
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """更新当前用户信息"""
    try:
        success = await auth_service.update_user(current_user["id"], user_data)
        if not success:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return APIResponse.success_response(message="用户信息更新成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/change-password", response_model=APIResponse)
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """修改密码"""
    try:
        success = await auth_service.change_password(
            current_user["id"],
            password_data.current_password,
            password_data.new_password
        )
        if not success:
            raise HTTPException(status_code=400, detail="当前密码错误")
        
        return APIResponse.success_response(message="密码修改成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/reset-password", response_model=APIResponse)
async def reset_password(
    reset_data: PasswordResetRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """重置密码请求"""
    try:
        success = await auth_service.request_password_reset(reset_data.email)
        return APIResponse.success_response(
            message="密码重置邮件已发送" if success else "如果邮箱存在，重置邮件已发送"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset-password/confirm", response_model=APIResponse)
async def confirm_password_reset(
    confirm_data: PasswordResetConfirm,
    auth_service: AuthService = Depends(get_auth_service)
):
    """确认密码重置"""
    try:
        success = await auth_service.confirm_password_reset(
            confirm_data.token,
            confirm_data.new_password
        )
        if not success:
            raise HTTPException(status_code=400, detail="重置令牌无效或已过期")
        
        return APIResponse.success_response(message="密码重置成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/permissions", response_model=UserPermissionsResponse)
async def get_user_permissions(
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """获取用户权限"""
    try:
        permissions = await auth_service.get_user_permissions(current_user["id"])
        return permissions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# API密钥管理
@router.post("/api-keys", response_model=APIResponse)
async def create_api_key(
    key_data: APIKeyCreate,
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """创建API密钥"""
    try:
        api_key = await auth_service.create_api_key(current_user["id"], key_data)
        return APIResponse.success_response(
            data={"api_key": api_key},
            message="API密钥创建成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/api-keys", response_model=ListResponse)
async def get_api_keys(
    pagination: PaginationParams = Depends(),
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """获取API密钥列表"""
    try:
        keys, total = await auth_service.get_api_keys(
            current_user["id"],
            offset=pagination.offset,
            limit=pagination.size
        )
        
        return ListResponse.create(
            items=keys,
            total=total,
            page=pagination.page,
            size=pagination.size
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/api-keys/{key_id}", response_model=APIResponse)
async def delete_api_key(
    key_id: int,
    current_user: dict = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """删除API密钥"""
    try:
        success = await auth_service.delete_api_key(current_user["id"], key_id)
        if not success:
            raise HTTPException(status_code=404, detail="API密钥不存在")
        
        return APIResponse.success_response(message="API密钥删除成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
