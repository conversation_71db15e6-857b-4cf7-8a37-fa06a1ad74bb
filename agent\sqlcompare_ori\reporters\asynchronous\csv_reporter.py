# reporters/asynchronous/csv_reporter.py
"""
异步CSV差异报告器
支持异步写入和批量处理
"""
import asyncio
import csv
import os
from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_reporter import AsyncFileReporter
from core.models import DiffResult


class AsyncCsvReporter(AsyncFileReporter):
    """异步CSV差异报告器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        初始化异步CSV报告器
        
        Args:
            config: 报告器配置，包含filepath等
            **kwargs: 其他参数
        """
        self.config = config or {}
        filepath = self.config.get('filepath', 'comparison_report.csv')
        
        super().__init__(filepath, **kwargs)
        
        # CSV特定配置
        self.delimiter = self.config.get('delimiter', ',')
        self.quotechar = self.config.get('quotechar', '"')
        self.encoding = self.config.get('encoding', 'utf-8-sig')  # 支持Excel
        self.include_timestamp = self.config.get('include_timestamp', True)
        self.include_header = self.config.get('include_header', True)
        
        # 字段配置
        self.field_names = self.config.get('field_names', [
            'Key', 'Status', 'Source_Value', 'Target_Value', 'Timestamp'
        ])
        
        # 内部状态
        self._csv_writer = None
        self._header_written = False
    
    async def initialize(self) -> bool:
        """初始化CSV报告器"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.filepath), exist_ok=True)
            
            # 使用aiofiles打开文件
            import aiofiles
            self._file_handle = await aiofiles.open(
                self.filepath, 'w', 
                encoding=self.encoding, 
                newline=''
            )
            
            self.logger.info(f"异步CSV报告器初始化成功: {self.filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"异步CSV报告器初始化失败: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    async def _write_header(self):
        """写入CSV头部"""
        if self.include_header and not self._header_written:
            header_line = self.delimiter.join(self.field_names) + '\n'
            await self._file_handle.write(header_line)
            self._header_written = True
    
    async def _write_footer(self):
        """写入CSV尾部（CSV通常不需要尾部）"""
        # CSV文件通常不需要特殊的尾部
        pass
    
    async def _write_batch_async(self, diffs: List[DiffResult]) -> bool:
        """
        异步批量写入差异到CSV文件
        
        Args:
            diffs: 差异列表
            
        Returns:
            写入是否成功
        """
        try:
            if not self._file_handle:
                raise RuntimeError("CSV文件未打开")
            
            # 确保头部已写入
            if not self._header_written:
                await self._write_header()
            
            # 批量写入数据
            lines = []
            for diff in diffs:
                line = self._diff_to_csv_line(diff)
                lines.append(line)
            
            # 一次性写入所有行
            content = ''.join(lines)
            await self._file_handle.write(content)
            await self._file_handle.flush()  # 确保数据写入磁盘
            
            return True
            
        except Exception as e:
            self.logger.error(f"CSV批量写入失败: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    def _diff_to_csv_line(self, diff: DiffResult) -> str:
        """
        将差异结果转换为CSV行
        
        Args:
            diff: 差异结果
            
        Returns:
            CSV行字符串
        """
        # 准备字段值
        values = []
        
        for field_name in self.field_names:
            if field_name == 'Key':
                values.append(self._escape_csv_value(diff.key))
            elif field_name == 'Status':
                values.append(self._escape_csv_value(diff.status))
            elif field_name == 'Source_Value':
                source_val = self._format_value(diff.value_a)
                values.append(self._escape_csv_value(source_val))
            elif field_name == 'Target_Value':
                target_val = self._format_value(diff.value_b)
                values.append(self._escape_csv_value(target_val))
            elif field_name == 'Timestamp':
                if self.include_timestamp:
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    values.append(self._escape_csv_value(timestamp))
                else:
                    values.append('')
            else:
                # 自定义字段，默认为空
                values.append('')
        
        return self.delimiter.join(values) + '\n'
    
    def _format_value(self, value) -> str:
        """
        格式化值为字符串
        
        Args:
            value: 要格式化的值
            
        Returns:
            格式化后的字符串
        """
        if value is None:
            return ''
        elif isinstance(value, (list, tuple)):
            # 将列表/元组转换为字符串
            return str(value)
        elif isinstance(value, str):
            return value
        else:
            return str(value)
    
    def _escape_csv_value(self, value: str) -> str:
        """
        转义CSV值
        
        Args:
            value: 要转义的值
            
        Returns:
            转义后的值
        """
        if not value:
            return ''
        
        # 如果值包含分隔符、引号或换行符，需要用引号包围
        if (self.delimiter in value or 
            self.quotechar in value or 
            '\n' in value or 
            '\r' in value):
            
            # 转义引号
            escaped_value = value.replace(self.quotechar, self.quotechar + self.quotechar)
            return f'{self.quotechar}{escaped_value}{self.quotechar}'
        
        return value
    
    async def get_file_info(self) -> Dict[str, Any]:
        """
        获取文件信息
        
        Returns:
            文件信息字典
        """
        try:
            file_stats = os.stat(self.filepath) if os.path.exists(self.filepath) else None
            
            return {
                'filepath': self.filepath,
                'exists': os.path.exists(self.filepath),
                'size_bytes': file_stats.st_size if file_stats else 0,
                'encoding': self.encoding,
                'delimiter': self.delimiter,
                'field_names': self.field_names,
                'header_written': self._header_written
            }
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return {'error': str(e)}
    
    async def validate_file(self) -> bool:
        """
        验证CSV文件格式
        
        Returns:
            文件是否有效
        """
        try:
            if not os.path.exists(self.filepath):
                return False
            
            # 读取文件的前几行进行验证
            import aiofiles
            async with aiofiles.open(self.filepath, 'r', encoding=self.encoding) as f:
                first_line = await f.readline()
                
                if self.include_header:
                    # 验证头部
                    expected_header = self.delimiter.join(self.field_names)
                    if first_line.strip() != expected_header:
                        self.logger.warning("CSV头部不匹配")
                        return False
                
                # 尝试读取第二行验证格式
                second_line = await f.readline()
                if second_line:
                    fields = second_line.split(self.delimiter)
                    if len(fields) != len(self.field_names):
                        self.logger.warning("CSV字段数量不匹配")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"CSV文件验证失败: {e}")
            return False


class AsyncCsvReporterFactory:
    """异步CSV报告器工厂"""
    
    @staticmethod
    def create_reporter(
        filepath: str,
        delimiter: str = ',',
        encoding: str = 'utf-8-sig',
        batch_size: int = 100,
        include_header: bool = True,
        include_timestamp: bool = True,
        field_names: Optional[List[str]] = None
    ) -> AsyncCsvReporter:
        """
        创建异步CSV报告器
        
        Args:
            filepath: 文件路径
            delimiter: 分隔符
            encoding: 编码
            batch_size: 批量大小
            include_header: 是否包含头部
            include_timestamp: 是否包含时间戳
            field_names: 字段名列表
            
        Returns:
            异步CSV报告器实例
        """
        config = {
            'filepath': filepath,
            'delimiter': delimiter,
            'encoding': encoding,
            'include_header': include_header,
            'include_timestamp': include_timestamp
        }
        
        if field_names:
            config['field_names'] = field_names
        
        return AsyncCsvReporter(config=config, batch_size=batch_size)
    
    @staticmethod
    def create_excel_compatible_reporter(
        filepath: str,
        batch_size: int = 100
    ) -> AsyncCsvReporter:
        """
        创建Excel兼容的CSV报告器
        
        Args:
            filepath: 文件路径
            batch_size: 批量大小
            
        Returns:
            Excel兼容的异步CSV报告器
        """
        config = {
            'filepath': filepath,
            'delimiter': ',',
            'encoding': 'utf-8-sig',  # Excel兼容的编码
            'include_header': True,
            'include_timestamp': True,
            'field_names': ['键值', '状态', '源端数据', '目标端数据', '时间戳']
        }
        
        return AsyncCsvReporter(config=config, batch_size=batch_size)


# 便捷函数
async def create_async_csv_report(
    filepath: str,
    diffs: List[DiffResult],
    **kwargs
) -> bool:
    """
    创建异步CSV报告的便捷函数
    
    Args:
        filepath: 文件路径
        diffs: 差异列表
        **kwargs: 其他参数
        
    Returns:
        报告是否创建成功
    """
    try:
        reporter = AsyncCsvReporterFactory.create_reporter(filepath, **kwargs)
        
        async with reporter:
            await reporter.report_diffs_async(diffs)
        
        return True
        
    except Exception as e:
        print(f"创建CSV报告失败: {e}")
        return False
