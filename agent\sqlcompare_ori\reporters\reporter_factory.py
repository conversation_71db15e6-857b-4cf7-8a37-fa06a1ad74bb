# reporters/reporter_factory.py
import logging
from typing import List, Dict, Any, Optional

from .base_reporter import BaseReporter
from .csv_reporter import CsvReporter
from .sqlite_reporter import SqliteReporter
from .postgres_reporter import PostgresReporter

logger = logging.getLogger(__name__)

class ReporterFactory:
    """报告器工厂，用于根据配置创建报告器实例"""

    @staticmethod
    def create_reporter(reporter_config: Dict[str, Any], task_id: str, table_name: str) -> Optional[BaseReporter]:
        """根据配置创建单个报告器实例"""
        reporter_type = reporter_config.get('type')
        try:
            if reporter_type == 'sqlite':
                config = {
                    'db_path': reporter_config.get('db_path', f'task_{task_id}_results.db'),
                    'task_id': task_id,
                    'table_name': table_name,
                    'batch_size': reporter_config.get('batch_size', 10000)
                }
                return SqliteReporter(config)
            elif reporter_type == 'postgresql':
                # 确保所有必要参数都存在
                required_keys = ['host', 'port', 'database', 'username', 'password']
                if not all(key in reporter_config for key in required_keys):
                    raise ValueError("PostgreSQL报告器缺少必要的配置参数")
                
                config = {
                    'host': reporter_config['host'],
                    'port': reporter_config['port'],
                    'database': reporter_config['database'],
                    'username': reporter_config['username'],
                    'password': reporter_config['password'],
                    'task_id': task_id,
                    'table_name': table_name,
                    'batch_size': reporter_config.get('batch_size', 10000)
                }
                return PostgresReporter(config)
            elif reporter_type == 'csv':
                config = {
                    'output_dir': reporter_config.get('output_dir', './exports/'),
                    'task_id': task_id,
                    'table_name': table_name,
                    'batch_size': reporter_config.get('batch_size', 10000)
                }
                return CsvReporter(config)
            else:
                logger.warning(f"不支持的报告器类型: {reporter_type}")
                return None
        except Exception as e:
            logger.error(f"创建报告器 '{reporter_type}' 失败: {e}", exc_info=True)
            # 可选：实现降级逻辑，例如，如果PostgreSQL失败，则尝试创建SQLite
            if reporter_config.get('fallback_to_sqlite', False):
                logger.info("尝试回退到SQLite报告器...")
                fallback_config = {'type': 'sqlite'}
                return ReporterFactory.create_reporter(fallback_config, task_id, table_name)
            return None

    @staticmethod
    def create_reporters(reporter_config: Dict[str, Any], task_id: str, table_name: str) -> List[BaseReporter]:
        """根据配置创建多个报告器实例"""
        reporters = []
        if not reporter_config or reporter_config.get('type') == 'sqlite': # 默认或显式SQLite
            # 保持向后兼容性
            sqlite_config = reporter_config if reporter_config else {}
            reporter = ReporterFactory.create_reporter(
                {'type': 'sqlite', **sqlite_config},
                task_id,
                table_name
            )
            if reporter:
                reporters.append(reporter)

        elif reporter_config.get('type') == 'multiple':
            for config in reporter_config.get('reporters', []):
                reporter = ReporterFactory.create_reporter(config, task_id, table_name)
                if reporter:
                    reporters.append(reporter)
        
        else: # 单一报告器配置
            reporter = ReporterFactory.create_reporter(reporter_config, task_id, table_name)
            if reporter:
                reporters.append(reporter)

        if not reporters:
            logger.warning("没有成功创建任何报告器，将使用默认的SQLite报告器")
            default_reporter = ReporterFactory.create_reporter({'type': 'sqlite'}, task_id, table_name)
            if default_reporter:
                reporters.append(default_reporter)

        return reporters