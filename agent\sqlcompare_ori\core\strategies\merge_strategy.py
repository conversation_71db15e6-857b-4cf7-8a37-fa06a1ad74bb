# core/strategies/merge_strategy.py
"""
归并比对策略
适用于大规模数据集，需要数据排序，内存占用低，流式处理
"""
import time
import logging
import sys
import os
from typing import Dict, Any, Optional, Callable

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.dirname(os.path.dirname(current_dir)))

from .base_strategy import ComparisonStrategy
from connectors.base_connector import BaseConnector
from reporters.base_reporter import BaseReporter
from core.models import DiffResult, Record
from core.engine import compare_values

logger = logging.getLogger(__name__)


class MergeComparisonStrategy(ComparisonStrategy):
    """归并比对策略实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.progress_interval = self.config.get('progress_interval', 50000)  # 进度报告间隔
    
    def get_strategy_name(self) -> str:
        return "Merge Comparison Strategy"
    
    def requires_sorting(self) -> bool:
        return True
    
    def estimate_memory_usage(self, record_count_a: int, record_count_b: int) -> int:
        """
        估算内存使用量
        归并算法只需要常数空间，主要是迭代器和缓冲区
        """
        base_memory = 1024 * 1024  # 1MB 基础内存
        buffer_memory = 1000 * 200  # 假设缓冲1000条记录，每条200字节
        return base_memory + buffer_memory
    
    def compare(
        self,
        source_a: BaseConnector,
        source_b: BaseConnector,
        reporter: BaseReporter,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        使用归并算法进行数据比对
        
        算法流程：
        1. 创建两个数据源的迭代器
        2. 使用归并算法逐条比对
        3. 实时报告差异
        
        前提条件：两个数据源必须按KEY排序
        """
        logger.info(f"开始使用{self.get_strategy_name()}进行数据比对...")
        start_time = time.perf_counter()
        
        # 统计信息
        stats = {
            'total_records_a': 0,
            'total_records_b': 0,
            'differences': 0,
            'fetch_time_a': 0.0,
            'fetch_time_b': 0.0,
            'compare_time': 0.0,
            'report_time': 0.0,
            'total_loop_time': 0.0
        }
        
        try:
            with source_a, source_b, reporter:
                # 创建迭代器
                iter_a = iter(source_a)
                iter_b = iter(source_b)
                
                # 获取第一条记录
                fetch_start_a = time.perf_counter()
                record_a: Optional[Record] = next(iter_a, None)
                stats['fetch_time_a'] += time.perf_counter() - fetch_start_a
                
                fetch_start_b = time.perf_counter()
                record_b: Optional[Record] = next(iter_b, None)
                stats['fetch_time_b'] += time.perf_counter() - fetch_start_b
                
                processed_count = 0
                last_log_time = start_time
                
                # 归并比对主循环
                while record_a is not None or record_b is not None:
                    loop_start_time = time.perf_counter()
                    
                    processed_count += 1
                    
                    # 进度报告
                    if processed_count % self.progress_interval == 0:
                        current_time = time.perf_counter()
                        duration = current_time - last_log_time
                        rate = self.progress_interval / duration if duration > 0 else float('inf')
                        
                        message = (f"已处理 {processed_count} 条记录... "
                                 f"当前速率: {rate:.2f} 条/秒. "
                                 f"差异数: {stats['differences']}. "
                                 f"A源记录: {stats['total_records_a']}, B源记录: {stats['total_records_b']}")
                        
                        logger.info(message)
                        if progress_callback:
                            progress_callback(message)
                        
                        last_log_time = current_time
                    
                    # 三路归并比对逻辑
                    if record_a and (record_b is None or record_a.key < record_b.key):
                        # A中独有的记录
                        stats['total_records_a'] += 1
                        
                        report_start = time.perf_counter()
                        diff = DiffResult(key=record_a.key, source_value=record_a.value, status='IN_A_ONLY')
                        reporter.report_diff(diff)
                        stats['differences'] += 1
                        stats['report_time'] += time.perf_counter() - report_start
                        
                        fetch_start_a = time.perf_counter()
                        record_a = next(iter_a, None)
                        stats['fetch_time_a'] += time.perf_counter() - fetch_start_a
                    
                    elif record_b and (record_a is None or record_b.key < record_a.key):
                        # B中独有的记录
                        stats['total_records_b'] += 1
                        
                        report_start = time.perf_counter()
                        diff = DiffResult(key=record_b.key, target_value=record_b.value, status='IN_B_ONLY')
                        reporter.report_diff(diff)
                        stats['differences'] += 1
                        stats['report_time'] += time.perf_counter() - report_start
                        
                        fetch_start_b = time.perf_counter()
                        record_b = next(iter_b, None)
                        stats['fetch_time_b'] += time.perf_counter() - fetch_start_b
                    
                    elif record_a and record_b:  # Keys are equal
                        # 键相同的记录
                        stats['total_records_a'] += 1
                        stats['total_records_b'] += 1
                        
                        compare_start = time.perf_counter()
                        are_equal = compare_values(record_a.value, record_b.value)
                        stats['compare_time'] += time.perf_counter() - compare_start
                        
                        if not are_equal:
                            report_start = time.perf_counter()
                            diff = DiffResult(
                                key=record_a.key,
                                source_value=record_a.value,
                                target_value=record_b.value,
                                status='DIFFERENT'
                            )
                            reporter.report_diff(diff)
                            stats['differences'] += 1
                            stats['report_time'] += time.perf_counter() - report_start
                        
                        fetch_start_a = time.perf_counter()
                        record_a = next(iter_a, None)
                        stats['fetch_time_a'] += time.perf_counter() - fetch_start_a
                        
                        fetch_start_b = time.perf_counter()
                        record_b = next(iter_b, None)
                        stats['fetch_time_b'] += time.perf_counter() - fetch_start_b
                    
                    stats['total_loop_time'] += time.perf_counter() - loop_start_time
                
        except Exception as e:
            logger.error(f"归并比对过程中发生错误: {e}", exc_info=True)
            raise
        finally:
            end_time = time.perf_counter()
            total_time = end_time - start_time
            
            # 输出统计信息
            logger.info("=" * 50)
            logger.info("归并比对完成")
            logger.info("=" * 50)
            logger.info(f"总耗时: {total_time:.2f} 秒")
            logger.info(f"A源记录数: {stats['total_records_a']}")
            logger.info(f"B源记录数: {stats['total_records_b']}")
            logger.info(f"总差异数: {stats['differences']}")
            
            if total_time > 0:
                total_records = stats['total_records_a'] + stats['total_records_b']
                rate = total_records / total_time
                logger.info(f"平均处理速率: {rate:.2f} 条/秒")
            
            logger.info("时间分布:")
            logger.info(f"  循环总耗时: {stats['total_loop_time']:.2f} 秒")
            logger.info(f"    - 数据获取 (A源): {stats['fetch_time_a']:.2f} 秒")
            logger.info(f"    - 数据获取 (B源): {stats['fetch_time_b']:.2f} 秒")
            logger.info(f"    - 数据比较: {stats['compare_time']:.2f} 秒")
            logger.info(f"    - 差异报告: {stats['report_time']:.2f} 秒")
            
            other_time = total_time - stats['total_loop_time']
            logger.info(f"  其他耗时 (初始化/关闭等): {other_time:.2f} 秒")
            
            # 内存使用估算
            estimated_memory = self.estimate_memory_usage(
                stats['total_records_a'], 
                stats['total_records_b']
            )
            logger.info(f"估算内存使用: {estimated_memory / 1024 / 1024:.1f} MB")
            
            stats['total_time'] = total_time
            stats['processing_rate'] = (stats['total_records_a'] + stats['total_records_b']) / total_time if total_time > 0 else 0
            
            return stats
