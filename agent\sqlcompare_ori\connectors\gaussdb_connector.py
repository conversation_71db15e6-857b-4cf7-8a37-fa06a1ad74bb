# connectors/gaussdb_connector.py

import psycopg2
import psycopg2.extras
from .base_connector import BaseConnector
from core.models import Record

class GaussDBConnector(BaseConnector):
    """Connector for GaussDB (using psycopg2, the PostgreSQL driver)."""

    def __init__(self, dsn, chunk_size=1000):
        super().__init__(chunk_size)
        self.dsn = dsn
        self.connection = None
        self.cursor = None

    def connect(self):
        """Establishes a connection to the GaussDB database."""
        try:
            self.connection = psycopg2.connect(self.dsn)
            print("GaussDB connection established.")
        except psycopg2.Error as e:
            print(f"Error connecting to GaussDB: {e}")
            raise

    def close(self):
        """Closes the database connection."""
        if self.cursor:
            self.cursor.close()
            self.cursor = None
        if self.connection:
            self.connection.close()
            self.connection = None
            print("GaussDB connection closed.")

    def fetch_data(self, query):
        """Fetches data from the database using the given query."""
        if not self.connection:
            self.connect()

        print(f"Executing query: {query}")
        # Use a named cursor for server-side processing to handle large datasets efficiently
        cursor_name = "gaussdb_fetch_cursor"
        self.cursor = self.connection.cursor(name=cursor_name, cursor_factory=psycopg2.extras.DictCursor)
        try:
            self.cursor.execute(query)
            return self
        except psycopg2.Error as e:
            print(f"Error executing query on GaussDB: {e}")
            self.close()
            raise

    def _fetch_next_chunk(self):
        """Fetches the next chunk of rows from the cursor."""
        try:
            rows = self.cursor.fetchmany(self.chunk_size)
            if not rows:
                return []
            # Verify 'key' and 'value' are present in the first row
            if rows and ('key' not in rows[0] or 'value' not in rows[0]):
                 raise ValueError("Query must select columns named 'key' and 'value'.")
            return [Record(key=row['key'], value=row['value']) for row in rows]
        except psycopg2.Error as e:
            print(f"Error fetching data from GaussDB: {e}")
            self.close()
            raise