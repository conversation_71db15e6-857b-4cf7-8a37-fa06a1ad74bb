# core/asynchronous/engine.py
import os
import sys
import time
import asyncio
import logging
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Tuple, Dict, Any
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, SCRIPT_DIR)
sys.path.insert(0, os.path.dirname(os.path.dirname(SCRIPT_DIR)))
from core.models import Record, DiffResult
from connectors.asynchronous.base_connector import AsyncBaseConnector
from reporters.asynchronous.base_reporter import AsyncBaseReporter

logger = logging.getLogger(__name__)


def compare_values(value_a, value_b) -> bool:
    """递归比较两个值是否相等，支持字典等复杂类型。"""
    # 首先检查是否完全相等（包括类型）
    if value_a == value_b:
        return True

    # 处理None值的特殊情况
    if value_a is None or value_b is None:
        return False

    # 类型不同时的处理策略
    if type(value_a) != type(value_b):
        # 类型不同，尝试转换为字符串进行比较
        return str(value_a) == str(value_b)

    if isinstance(value_a, dict):
        if value_a.keys() != value_b.keys():
            return False
        for k in value_a:
            if not compare_values(value_a[k], value_b[k]):
                return False
        return True

    # 对于其他基础类型，直接比较
    return value_a == value_b


class DataSourceStatus(Enum):
    """数据源状态枚举"""
    ACTIVE = "active"
    EXHAUSTED = "exhausted"
    ERROR = "error"


@dataclass
class DataItem:
    """数据项包装器，包含数据和来源信息"""
    record: Optional[Record]
    source_id: str  # 'A' or 'B'
    status: DataSourceStatus
    error: Optional[Exception] = None


@dataclass
class ComparisonConfig:
    """比对配置"""
    buffer_size: int = 1000  # 缓冲区大小
    max_concurrent_reads: int = 2  # 最大并发读取数
    read_timeout: float = 30.0  # 读取超时时间
    enable_progress_callback: bool = True
    memory_limit_mb: int = 100  # 内存限制


class AsyncComparisonEngine:
    """异步并发比对引擎"""
    
    def __init__(self, config: Optional[ComparisonConfig] = None):
        self.config = config or ComparisonConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 性能指标
        self.metrics = {
            'total_records_a': 0,
            'total_records_b': 0,
            'differences': 0,
            'fetch_time_a': 0.0,
            'fetch_time_b': 0.0,
            'compare_time': 0.0,
            'report_time': 0.0,
            'total_time': 0.0,
            'processing_rate': 0.0,
            'concurrent_efficiency': 0.0
        }
    
    async def compare_sources(
        self,
        source_a: AsyncBaseConnector,
        source_b: AsyncBaseConnector,
        reporter: AsyncBaseReporter,
        progress_callback: Optional[callable] = None
    ) -> dict:
        """
        异步并发比对两个数据源
        
        Args:
            source_a: 数据源A的异步连接器
            source_b: 数据源B的异步连接器  
            reporter: 异步差异报告器
            progress_callback: 进度回调函数
            
        Returns:
            比对结果统计信息
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info("开始异步并发数据比对...")
            
            # 创建数据队列
            queue_a = asyncio.Queue(maxsize=self.config.buffer_size)
            queue_b = asyncio.Queue(maxsize=self.config.buffer_size)
            
            # 启动生产者任务
            producer_a = asyncio.create_task(
                self._data_producer(source_a, queue_a, 'A')
            )
            producer_b = asyncio.create_task(
                self._data_producer(source_b, queue_b, 'B')
            )
            
            # 启动消费者任务（比对引擎）
            consumer_task = asyncio.create_task(
                self._comparison_consumer(queue_a, queue_b, reporter, progress_callback)
            )
            
            # 等待所有任务完成
            await asyncio.gather(producer_a, producer_b, consumer_task)
            
            # 计算性能指标
            end_time = asyncio.get_event_loop().time()
            self.metrics['total_time'] = end_time - start_time

            # 计算处理速率
            total_records = self.metrics['total_records_a'] + self.metrics['total_records_b']
            if self.metrics['total_time'] > 0:
                self.metrics['processing_rate'] = total_records / self.metrics['total_time']

            self.metrics['concurrent_efficiency'] = self._calculate_efficiency()

            # 输出详细统计信息
            self.logger.info("=" * 50)
            self.logger.info("异步并发比对完成")
            self.logger.info("=" * 50)
            self.logger.info(f"总耗时: {self.metrics['total_time']:.2f} 秒")
            self.logger.info(f"A源记录数: {self.metrics['total_records_a']:,}")
            self.logger.info(f"B源记录数: {self.metrics['total_records_b']:,}")
            self.logger.info(f"总差异数: {self.metrics['differences']:,}")

            if self.metrics['total_time'] > 0:
                self.logger.info(f"平均处理速率: {self.metrics['processing_rate']:.2f} 条/秒")

            self.logger.info("时间分布:")
            self.logger.info(f"  数据比较: {self.metrics['compare_time']:.2f} 秒")
            self.logger.info(f"  差异报告: {self.metrics['report_time']:.2f} 秒")
            self.logger.info(f"  并发效率: {self.metrics['concurrent_efficiency']:.2f}")

            return self.metrics
            
        except Exception as e:
            self.logger.error(f"异步比对过程中发生错误: {e}")
            raise
    
    async def _data_producer(
        self,
        connector: AsyncBaseConnector,
        queue: asyncio.Queue,
        source_id: str
    ):
        """
        数据生产者：从连接器读取数据并放入队列
        
        Args:
            connector: 异步连接器
            queue: 数据队列
            source_id: 数据源标识 ('A' or 'B')
        """
        try:
            async with connector:
                async for record in connector.fetch_data_async():
                    # 创建数据项
                    data_item = DataItem(
                        record=record,
                        source_id=source_id,
                        status=DataSourceStatus.ACTIVE
                    )
                    
                    # 放入队列（如果队列满了会等待）
                    await queue.put(data_item)

                    # 更新统计
                    if source_id == 'A':
                        self.metrics['total_records_a'] += 1
                    else:
                        self.metrics['total_records_b'] += 1
                
                # 数据源耗尽，发送结束信号
                end_item = DataItem(
                    record=None,
                    source_id=source_id,
                    status=DataSourceStatus.EXHAUSTED
                )
                await queue.put(end_item)
                
        except Exception as e:
            self.logger.error(f"数据源{source_id}读取失败: {e}")
            # 发送错误信号
            error_item = DataItem(
                record=None,
                source_id=source_id,
                status=DataSourceStatus.ERROR,
                error=e
            )
            await queue.put(error_item)
    
    async def _comparison_consumer(
        self,
        queue_a: asyncio.Queue,
        queue_b: asyncio.Queue,
        reporter: AsyncBaseReporter,
        progress_callback: Optional[callable]
    ):
        """
        比对消费者：从两个队列获取数据进行比对
        
        Args:
            queue_a: 数据源A的队列
            queue_b: 数据源B的队列
            reporter: 异步报告器
            progress_callback: 进度回调函数
        """
        current_a: Optional[DataItem] = None
        current_b: Optional[DataItem] = None
        source_a_exhausted = False
        source_b_exhausted = False
        
        try:
            async with reporter:
                while not (source_a_exhausted and source_b_exhausted):
                    # 获取当前数据项
                    if current_a is None and not source_a_exhausted:
                        current_a = await queue_a.get()
                        if current_a.status != DataSourceStatus.ACTIVE:
                            source_a_exhausted = True
                            current_a = None
                    
                    if current_b is None and not source_b_exhausted:
                        current_b = await queue_b.get()
                        if current_b.status != DataSourceStatus.ACTIVE:
                            source_b_exhausted = True
                            current_b = None
                    
                    # 执行比对逻辑（保持原有的归并算法）
                    diff_result = await self._compare_records(current_a, current_b)
                    
                    if diff_result:
                        report_start = time.perf_counter()
                        await reporter.report_diff_async(diff_result)
                        self.metrics['report_time'] += time.perf_counter() - report_start
                        self.metrics['differences'] += 1

                    # 推进指针
                    current_a, current_b = self._advance_pointers(current_a, current_b)

                    # 更新进度（每1000条记录报告一次）
                    total_processed = self.metrics['total_records_a'] + self.metrics['total_records_b']
                    if progress_callback and total_processed % 1000 == 0:
                        await self._call_progress_callback(progress_callback)
                        
        except Exception as e:
            self.logger.error(f"比对消费者处理失败: {e}")
            raise
    
    async def _compare_records(
        self,
        item_a: Optional[DataItem],
        item_b: Optional[DataItem]
    ) -> Optional[DiffResult]:
        """
        比对两条记录，实现原有的归并算法逻辑
        
        Args:
            item_a: 数据源A的数据项
            item_b: 数据源B的数据项
            
        Returns:
            差异结果，如果没有差异则返回None
        """
        record_a = item_a.record if item_a else None
        record_b = item_b.record if item_b else None
        
        if record_a and (record_b is None or record_a.key < record_b.key):
            # A中独有的记录
            return DiffResult(
                key=record_a.key,
                value_a=record_a.value,
                status='IN_A_ONLY'
            )
        elif record_b and (record_a is None or record_b.key < record_a.key):
            # B中独有的记录
            return DiffResult(
                key=record_b.key,
                value_b=record_b.value,
                status='IN_B_ONLY'
            )
        elif record_a and record_b:  # Keys are equal
            if not compare_values(record_a.value, record_b.value):
                # 键相同但值不同（使用改进的比较函数）
                return DiffResult(
                    key=record_a.key,
                    value_a=record_a.value,
                    value_b=record_b.value,
                    status='DIFFERENT'
                )
        
        return None
    
    def _advance_pointers(
        self,
        current_a: Optional[DataItem],
        current_b: Optional[DataItem]
    ) -> Tuple[Optional[DataItem], Optional[DataItem]]:
        """
        根据比对结果推进数据指针
        
        Args:
            current_a: 当前数据源A的数据项
            current_b: 当前数据源B的数据项
            
        Returns:
            更新后的数据指针
        """
        if not current_a:
            return None, None
        if not current_b:
            return None, current_b
        
        record_a = current_a.record
        record_b = current_b.record
        
        if record_a.key < record_b.key:
            return None, current_b  # 推进A
        elif record_b.key < record_a.key:
            return current_a, None  # 推进B
        else:
            return None, None  # 键相同，同时推进
    
    async def _call_progress_callback(self, progress_callback: callable):
        """调用进度回调函数"""
        try:
            if asyncio.iscoroutinefunction(progress_callback):
                await progress_callback(self.metrics)
            else:
                progress_callback(self.metrics)
        except Exception as e:
            self.logger.warning(f"进度回调函数执行失败: {e}")
    
    def _calculate_efficiency(self) -> float:
        """计算并发效率"""
        total_records = self.metrics['total_records_a'] + self.metrics['total_records_b']
        if total_records == 0 or self.metrics['total_time'] == 0:
            return 0.0

        # 并发效率计算：考虑异步处理的优势
        base_rate = total_records / self.metrics['total_time']

        # 如果有并发处理时间统计，可以计算更精确的效率
        # 这里简化为基础处理速率
        return base_rate

    async def compare_sources_memory_dict(
        self,
        source_a: AsyncBaseConnector,
        source_b: AsyncBaseConnector,
        reporter: AsyncBaseReporter,
        progress_callback: Optional[callable] = None
    ) -> dict:
        """
        异步内存字典比对方法
        完全模拟同步版本的thread_control风格实现

        Args:
            source_a: 数据源A的异步连接器
            source_b: 数据源B的异步连接器
            reporter: 异步差异报告器
            progress_callback: 进度回调函数

        Returns:
            比对结果统计信息
        """
        start_time = asyncio.get_event_loop().time()

        try:
            self.logger.info("开始异步内存字典比对...")

            # 阶段1：异步加载数据源A到共享字典
            self.logger.info("异步加载数据源A...")
            load_start = asyncio.get_event_loop().time()

            # 创建共享字典
            data_result = await source_a.fetch_all_data(shared_dict=None, data_source='t1')
            total_records_a = len(data_result)

            load_a_time = asyncio.get_event_loop().time() - load_start
            self.logger.info(f"数据源A加载完成: {total_records_a:,} 条记录，耗时: {load_a_time:.2f}秒")

            # 阶段2：异步加载数据源B到同一个字典
            self.logger.info("异步加载数据源B...")
            load_start = asyncio.get_event_loop().time()

            # 使用共享字典加载数据源B
            data_result = await source_b.fetch_all_data(shared_dict=data_result, data_source='t2')
            total_records_b = sum(1 for item in data_result.values() if item.get('t2', '') != '')

            load_b_time = asyncio.get_event_loop().time() - load_start
            self.logger.info(f"数据源B加载完成: {total_records_b:,} 条记录，耗时: {load_b_time:.2f}秒")

            # 阶段3：异步报告差异（比对已在数据加载时完成）
            self.logger.info("异步报告差异结果...")
            report_start = asyncio.get_event_loop().time()
            diff_count = len(data_result)  # 字典中只包含有差异的记录
            processed = 0

            async with reporter:
                # 遍历所有差异记录进行报告
                for key, item in data_result.items():
                    processed += 1

                    # 确定差异类型
                    if item["t1"] == "":
                        status = 'IN_B_ONLY'
                        value_a = None
                        value_b = item["t2"]
                    elif item["t2"] == "":
                        status = 'IN_A_ONLY'
                        value_a = item["t1"]
                        value_b = None
                    else:
                        status = 'DIFFERENT'
                        value_a = item["t1"]
                        value_b = item["t2"]

                    # 创建差异结果
                    diff = DiffResult(
                        key=key,
                        value_a=value_a,
                        value_b=value_b,
                        status=status
                    )

                    # 异步报告差异
                    await reporter.report_diff_async(diff)

                    # 定期输出进度和调用回调
                    if processed % 100000 == 0:
                        self.logger.info(f"已报告 {processed:,}/{diff_count:,} 条差异记录")
                        if progress_callback:
                            await self._call_progress_callback(progress_callback)

            report_time = asyncio.get_event_loop().time() - report_start
            self.logger.info(f"差异报告完成，耗时: {report_time:.2f}秒")

            # 计算性能指标
            end_time = asyncio.get_event_loop().time()
            total_time = end_time - start_time

            # 更新指标
            self.metrics.update({
                'total_records_a': total_records_a,
                'total_records_b': total_records_b,
                'differences': diff_count,
                'total_time': total_time,
                'compare_time': load_a_time + load_b_time,
                'report_time': report_time
            })

            # 计算处理速率
            total_records = total_records_a + total_records_b
            if total_time > 0:
                self.metrics['processing_rate'] = total_records / total_time

            self.metrics['concurrent_efficiency'] = self._calculate_efficiency()

            # 输出最终统计
            self.logger.info("异步内存字典比对完成")
            self.logger.info(f"总耗时: {total_time:.2f}秒，处理记录: {total_records:,}条")
            self.logger.info(f"A源: {total_records_a:,}条，B源: {total_records_b:,}条，差异: {diff_count:,}条")
            self.logger.info(f"时间分布 - 加载A: {load_a_time:.2f}s, 加载B: {load_b_time:.2f}s, 报告: {report_time:.2f}s")

            return self.metrics

        except Exception as e:
            self.logger.error(f"异步内存字典比对过程中发生错误: {e}")
            raise


# 便捷函数
async def compare_sources_async(
    source_a: AsyncBaseConnector,
    source_b: AsyncBaseConnector,
    reporter: AsyncBaseReporter,
    config: Optional[ComparisonConfig] = None,
    progress_callback: Optional[callable] = None
) -> dict:
    """
    异步比对两个数据源的便捷函数
    
    Args:
        source_a: 数据源A的异步连接器
        source_b: 数据源B的异步连接器
        reporter: 异步差异报告器
        config: 比对配置
        progress_callback: 进度回调函数
        
    Returns:
        比对结果统计信息
    """
    engine = AsyncComparisonEngine(config)
    return await engine.compare_sources(source_a, source_b, reporter, progress_callback)
