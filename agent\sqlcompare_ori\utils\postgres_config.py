#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL配置工具
提供PostgreSQL数据库连接配置和管理功能
"""

import os
import logging
from typing import Dict, Any, Optional
from urllib.parse import quote_plus

logger = logging.getLogger(__name__)


class PostgreSQLConfig:
    """PostgreSQL配置管理器"""

    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化PostgreSQL配置

        Args:
            config: 配置字典，包含host、port、database、username、password等
        """
        self.config = config or {}
        self._load_from_environment()

    def _load_from_environment(self):
        """从环境变量加载配置"""
        env_mapping = {
            'host': ['POSTGRES_HOST', 'PGHOST'],
            'port': ['POSTGRES_PORT', 'PGPORT'],
            'database': ['POSTGRES_DB', 'POSTGRES_DATABASE', 'PGDATABASE'],
            'username': ['POSTGRES_USER', 'POSTGRES_USERNAME', 'PGUSER'],
            'password': ['POSTGRES_PASSWORD', 'PGPASSWORD'],
            'schema': ['POSTGRES_SCHEMA', 'PGSCHEMA']
        }

        for key, env_vars in env_mapping.items():
            if key not in self.config:
                for env_var in env_vars:
                    value = os.getenv(env_var)
                    if value:
                        self.config[key] = value
                        logger.debug(f"从环境变量 {env_var} 加载配置: {key}")
                        break

        # 设置默认值
        self.config.setdefault('host', 'localhost')
        self.config.setdefault('port', 5432)
        self.config.setdefault('schema', 'public')

    def get_database_url(self) -> str:
        """
        构建PostgreSQL数据库URL

        Returns:
            PostgreSQL连接URL字符串

        Raises:
            ValueError: 当必需的配置缺失时
        """
        required_fields = ['database', 'username', 'password']
        missing_fields = [field for field in required_fields if not self.config.get(field)]

        if missing_fields:
            raise ValueError(f"PostgreSQL配置缺失必需字段: {missing_fields}")

        # URL编码特殊字符
        username = quote_plus(str(self.config['username']))
        password = quote_plus(str(self.config['password']))
        host = self.config['host']
        port = self.config['port']
        database = self.config['database']

        url = f"postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}"

        # 添加schema参数（如果不是默认的public）
        if self.config.get('schema') and self.config['schema'] != 'public':
            url += f"?options=-csearch_path%3D{self.config['schema']}"

        return url

    def get_connection_params(self) -> Dict[str, Any]:
        """
        获取连接参数字典

        Returns:
            连接参数字典
        """
        return {
            'host': self.config.get('host'),
            'port': int(self.config.get('port', 5432)),
            'database': self.config.get('database'),
            'username': self.config.get('username'),
            'password': self.config.get('password'),
            'schema': self.config.get('schema', 'public')
        }

    def test_connection(self) -> bool:
        """
        测试PostgreSQL连接

        Returns:
            连接是否成功
        """
        try:
            import psycopg2
            
            conn_params = self.get_connection_params()
            # 移除schema参数，psycopg2不直接支持
            schema = conn_params.pop('schema', 'public')
            
            # 建立连接
            conn = psycopg2.connect(
                host=conn_params['host'],
                port=conn_params['port'],
                database=conn_params['database'],
                user=conn_params['username'],
                password=conn_params['password']
            )
            
            # 测试查询
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            logger.info("PostgreSQL连接测试成功")
            return result[0] == 1

        except ImportError:
            logger.error("psycopg2未安装，无法测试PostgreSQL连接")
            return False
        except Exception as e:
            logger.error(f"PostgreSQL连接测试失败: {e}")
            return False

    def validate_config(self) -> tuple[bool, list[str]]:
        """
        验证配置完整性

        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []

        # 检查必需字段
        required_fields = ['host', 'port', 'database', 'username', 'password']
        for field in required_fields:
            if not self.config.get(field):
                errors.append(f"缺失必需字段: {field}")

        # 检查端口号
        try:
            port = int(self.config.get('port', 5432))
            if not (1 <= port <= 65535):
                errors.append(f"端口号无效: {port}")
        except (ValueError, TypeError):
            errors.append(f"端口号格式错误: {self.config.get('port')}")

        # 检查数据库名称
        database = self.config.get('database', '')
        if database and not database.replace('_', '').replace('-', '').isalnum():
            errors.append(f"数据库名称包含无效字符: {database}")

        return len(errors) == 0, errors

    def get_reporter_config(self, task_id: str, comparison_table: str = None) -> Dict[str, Any]:
        """
        获取PostgreSQL报告器配置

        Args:
            task_id: 任务ID
            comparison_table: 比对表名

        Returns:
            报告器配置字典
        """
        base_config = self.get_connection_params()
        
        reporter_config = {
            **base_config,
            'task_id': task_id,
            'table_name': 'comparison_results',
            'comparison_table': comparison_table or 'unknown',
            'batch_size': 50000,
            'use_copy': True,
            'high_performance_mode': True,
            'append_mode': False,
            'silent_mode': False
        }

        return reporter_config

    def __str__(self) -> str:
        """字符串表示（隐藏密码）"""
        safe_config = self.config.copy()
        if 'password' in safe_config:
            safe_config['password'] = '***'
        return f"PostgreSQLConfig({safe_config})"

    def __repr__(self) -> str:
        return self.__str__()


def create_postgres_config_from_env() -> PostgreSQLConfig:
    """
    从环境变量创建PostgreSQL配置

    Returns:
        PostgreSQL配置实例
    """
    return PostgreSQLConfig()


def create_postgres_config(host: str, port: int, database: str, 
                          username: str, password: str, schema: str = 'public') -> PostgreSQLConfig:
    """
    创建PostgreSQL配置

    Args:
        host: 主机地址
        port: 端口号
        database: 数据库名
        username: 用户名
        password: 密码
        schema: 模式名

    Returns:
        PostgreSQL配置实例
    """
    config = {
        'host': host,
        'port': port,
        'database': database,
        'username': username,
        'password': password,
        'schema': schema
    }
    return PostgreSQLConfig(config)


def get_postgres_url_from_config(config: Dict[str, Any]) -> str:
    """
    从配置字典构建PostgreSQL URL

    Args:
        config: 配置字典

    Returns:
        PostgreSQL连接URL
    """
    pg_config = PostgreSQLConfig(config)
    return pg_config.get_database_url()


# 示例配置
EXAMPLE_CONFIGS = {
    'local_development': {
        'host': 'localhost',
        'port': 5432,
        'database': 'sqlcompare_dev',
        'username': 'postgres',
        'password': 'dev_password',
        'schema': 'public'
    },
    'production': {
        'host': 'prod-postgres.example.com',
        'port': 5432,
        'database': 'sqlcompare_prod',
        'username': 'sqlcompare_user',
        'password': 'secure_password',
        'schema': 'sqlcompare'
    },
    'docker_compose': {
        'host': 'postgres',
        'port': 5432,
        'database': 'sqlcompare',
        'username': 'postgres',
        'password': 'postgres',
        'schema': 'public'
    }
}


if __name__ == "__main__":
    # 测试示例
    print("PostgreSQL配置工具测试")
    print("=" * 40)
    
    # 测试环境变量配置
    env_config = create_postgres_config_from_env()
    print(f"环境变量配置: {env_config}")
    
    # 测试手动配置
    manual_config = create_postgres_config(
        host='localhost',
        port=5432,
        database='test_db',
        username='test_user',
        password='test_pass'
    )
    print(f"手动配置: {manual_config}")
    
    # 验证配置
    is_valid, errors = manual_config.validate_config()
    print(f"配置验证: {'有效' if is_valid else '无效'}")
    if errors:
        print(f"错误: {errors}")
    
    # 生成数据库URL
    try:
        url = manual_config.get_database_url()
        print(f"数据库URL: {url}")
    except ValueError as e:
        print(f"URL生成失败: {e}")
