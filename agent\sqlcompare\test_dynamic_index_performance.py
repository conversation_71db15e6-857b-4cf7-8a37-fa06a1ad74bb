# test_dynamic_index_performance.py - 测试动态索引管理性能
"""
测试动态索引管理的性能提升效果

对比测试：
1. 标准模式（有索引插入）
2. 动态索引管理模式（删除索引->插入->重建索引）
"""
import os
import sys
import time
import tempfile
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from reporters.sqlite_reporter import SqliteReporter
from core.models import DiffResult

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def generate_test_data(count: int) -> list:
    """生成测试数据"""
    test_data = []
    statuses = ['IDENTICAL', 'DIFFERENT', 'SOURCE_ONLY', 'TARGET_ONLY']
    tables = ['users', 'orders', 'products', 'customers', 'inventory']
    fields = ['id', 'name', 'value', 'status', 'created_at', 'updated_at']
    
    for i in range(count):
        diff_result = DiffResult(
            table_name=f"{tables[i % len(tables)]}",
            record_key=f"key_{i}",
            status=statuses[i % len(statuses)],
            field_name=fields[i % len(fields)],
            source_value=f"source_value_{i}" if i % 3 != 0 else None,
            target_value=f"target_value_{i}" if i % 4 != 0 else None
        )
        test_data.append(diff_result)
    
    return test_data


def test_standard_mode_performance(test_data, db_path):
    """测试标准模式性能（有索引插入）"""
    logger.info("=== 测试标准模式性能（有索引插入） ===")
    
    config = {
        'db_path': db_path,
        'task_id': 'test_standard',
        'batch_size': 10000,
        'high_performance_mode': False,  # 关闭高性能模式，保持索引
        'append_mode': False
    }
    
    reporter = SqliteReporter(config)
    
    start_time = time.time()
    
    # 打开报告器
    reporter.open()
    
    # 插入数据
    for diff_result in test_data:
        reporter.report_diff(diff_result)
    
    # 关闭报告器
    reporter.close()
    
    elapsed_time = time.time() - start_time
    records_per_second = len(test_data) / elapsed_time
    
    logger.info(f"标准模式 - 总耗时: {elapsed_time:.3f}秒")
    logger.info(f"标准模式 - 插入速度: {records_per_second:.0f} 记录/秒")
    
    return {
        'mode': 'standard',
        'total_time': elapsed_time,
        'records_per_second': records_per_second,
        'record_count': len(test_data)
    }


def test_dynamic_index_mode_performance(test_data, db_path):
    """测试动态索引管理模式性能"""
    logger.info("=== 测试动态索引管理模式性能 ===")
    
    config = {
        'db_path': db_path,
        'task_id': 'test_dynamic',
        'batch_size': 10000,
        'high_performance_mode': True,  # 启用高性能模式，使用动态索引管理
        'append_mode': False
    }
    
    reporter = SqliteReporter(config)
    
    start_time = time.time()
    
    # 打开报告器（会删除索引）
    open_start = time.time()
    reporter.open()
    open_time = time.time() - open_start
    
    # 插入数据
    insert_start = time.time()
    for diff_result in test_data:
        reporter.report_diff(diff_result)
    insert_time = time.time() - insert_start
    
    # 关闭报告器（会重建索引）
    close_start = time.time()
    reporter.close()
    close_time = time.time() - close_start
    
    total_time = time.time() - start_time
    records_per_second = len(test_data) / total_time
    insert_speed = len(test_data) / insert_time
    
    logger.info(f"动态索引模式 - 打开耗时: {open_time:.3f}秒")
    logger.info(f"动态索引模式 - 插入耗时: {insert_time:.3f}秒")
    logger.info(f"动态索引模式 - 关闭耗时: {close_time:.3f}秒")
    logger.info(f"动态索引模式 - 总耗时: {total_time:.3f}秒")
    logger.info(f"动态索引模式 - 纯插入速度: {insert_speed:.0f} 记录/秒")
    logger.info(f"动态索引模式 - 整体速度: {records_per_second:.0f} 记录/秒")
    
    return {
        'mode': 'dynamic_index',
        'total_time': total_time,
        'open_time': open_time,
        'insert_time': insert_time,
        'close_time': close_time,
        'records_per_second': records_per_second,
        'insert_speed': insert_speed,
        'record_count': len(test_data)
    }


def test_query_performance(db_path):
    """测试查询性能（验证索引重建效果）"""
    logger.info("=== 测试查询性能（验证索引重建效果） ===")
    
    config = {
        'db_path': db_path,
        'task_id': 'test_query'
    }
    
    reporter = SqliteReporter(config)
    
    # 测试不同类型的查询
    queries = [
        ("按表名查询", {'table_name': 'users', 'limit': 1000}),
        ("按状态查询", {'status': 'DIFFERENT', 'limit': 1000}),
        ("分页查询", {'limit': 500, 'offset': 1000}),
        ("大量数据查询", {'limit': 5000})
    ]
    
    query_results = []
    
    for query_name, params in queries:
        start_time = time.time()
        results, total = reporter.get_comparison_results(**params)
        elapsed_time = time.time() - start_time
        
        logger.info(f"{query_name} - 耗时: {elapsed_time:.3f}秒, 结果: {len(results)} 条")
        
        query_results.append({
            'query': query_name,
            'time': elapsed_time,
            'result_count': len(results),
            'total_count': total
        })
    
    return query_results


def main():
    """主测试函数"""
    logger.info("开始动态索引管理性能测试...")
    
    # 测试参数
    test_record_counts = [10000, 50000, 100000]
    
    all_results = []
    
    for record_count in test_record_counts:
        logger.info(f"\n{'='*60}")
        logger.info(f"测试记录数: {record_count}")
        logger.info(f"{'='*60}")
        
        # 生成测试数据
        test_data = generate_test_data(record_count)
        
        # 标准模式测试
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            standard_db_path = tmp_file.name
        
        try:
            standard_result = test_standard_mode_performance(test_data, standard_db_path)
        finally:
            if os.path.exists(standard_db_path):
                os.unlink(standard_db_path)
        
        # 动态索引模式测试
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            dynamic_db_path = tmp_file.name
        
        try:
            dynamic_result = test_dynamic_index_mode_performance(test_data, dynamic_db_path)
            
            # 查询性能测试
            query_results = test_query_performance(dynamic_db_path)
            dynamic_result['query_results'] = query_results
            
        finally:
            if os.path.exists(dynamic_db_path):
                os.unlink(dynamic_db_path)
        
        # 性能对比
        speedup = dynamic_result['records_per_second'] / standard_result['records_per_second']
        insert_speedup = dynamic_result['insert_speed'] / standard_result['records_per_second']
        
        logger.info(f"\n--- 性能对比结果 ({record_count} 条记录) ---")
        logger.info(f"标准模式速度: {standard_result['records_per_second']:.0f} 记录/秒")
        logger.info(f"动态索引模式整体速度: {dynamic_result['records_per_second']:.0f} 记录/秒")
        logger.info(f"动态索引模式纯插入速度: {dynamic_result['insert_speed']:.0f} 记录/秒")
        logger.info(f"整体性能提升: {speedup:.2f}x")
        logger.info(f"纯插入性能提升: {insert_speedup:.2f}x")
        
        all_results.append({
            'record_count': record_count,
            'standard': standard_result,
            'dynamic': dynamic_result,
            'speedup': speedup,
            'insert_speedup': insert_speedup
        })
    
    # 总结报告
    logger.info(f"\n{'='*60}")
    logger.info("动态索引管理性能测试总结")
    logger.info(f"{'='*60}")
    
    for result in all_results:
        logger.info(f"记录数: {result['record_count']:,}")
        logger.info(f"  整体性能提升: {result['speedup']:.2f}x")
        logger.info(f"  纯插入性能提升: {result['insert_speedup']:.2f}x")
        logger.info(f"  索引重建耗时: {result['dynamic']['close_time']:.2f}秒")
    
    # 验证功能正确性
    logger.info(f"\n--- 功能验证 ---")
    if all_results:
        last_result = all_results[-1]
        if last_result['dynamic']['query_results']:
            logger.info("✓ 查询功能正常，索引重建成功")
            for query_result in last_result['dynamic']['query_results']:
                logger.info(f"  {query_result['query']}: {query_result['time']:.3f}秒")
        else:
            logger.warning("⚠ 查询测试未执行")
    
    logger.info("\n🎉 动态索引管理性能测试完成！")
    
    return all_results


if __name__ == '__main__':
    try:
        results = main()
        sys.exit(0)
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
