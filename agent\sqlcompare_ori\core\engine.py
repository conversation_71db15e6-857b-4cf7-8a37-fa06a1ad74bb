# core/engine.py
import time
import logging
from typing import Optional
from core.models import DiffResult, Record
from connectors.base_connector import BaseConnector
from reporters.base_reporter import BaseReporter

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def compare_values(value_a, value_b) -> bool:
    """极致优化的值比较函数，最大化性能。"""
    # 最快的比较：身份比较
    if value_a is value_b:
        return True

    # 快速None检查
    if value_a is None or value_b is None:
        return value_a is value_b

    # 类型检查优化
    type_a, type_b = type(value_a), type(value_b)
    if type_a is type_b:
        if type_a is dict:
            # 字典比较优化
            if len(value_a) != len(value_b):
                return False
            # 使用viewkeys()比较键（Python 2/3兼容）
            if value_a.keys() != value_b.keys():
                return False
            # 批量比较值
            for k in value_a:
                if not compare_values(value_a[k], value_b[k]):
                    return False
            return True
        elif type_a in (str, int, float, bool):
            # 基础类型直接比较
            return value_a == value_b
        else:
            # 其他类型
            return value_a == value_b

    # 类型不同时的快速字符串比较
    try:
        # 避免重复的str()调用
        if type_a is str:
            str_a = value_a
        else:
            str_a = str(value_a)

        if type_b is str:
            str_b = value_b
        else:
            str_b = str(value_b)

        return str_a == str_b
    except:
        return False

def compare_sources(source_a: BaseConnector, source_b: BaseConnector, reporter: BaseReporter):
    """
    核心比对引擎，使用流式归并算法比较两个已排序的数据源。

    :param source_a: 数据源 A 的连接器实例。
    :param source_b: 数据源 B 的连接器实例。
    :param reporter: 差异报告器实例。
    """
    logger.info("开始进行数据比对...")
    start_time = time.perf_counter()
    
    total_records_a = 0
    total_records_b = 0
    diff_count = 0

    try:
        with source_a, source_b, reporter:
            iter_a = iter(source_a)
            iter_b = iter(source_b)

            record_a: Optional[Record] = next(iter_a, None)
            record_b: Optional[Record] = next(iter_b, None)

            processed_count = 0
            last_log_time = start_time

            while record_a is not None or record_b is not None:
                processed_count += 1
                
                # 简化进度监控，降低监控频率
                if processed_count % 100000 == 0:
                    current_time = time.perf_counter()
                    duration = current_time - last_log_time
                    rate = 100000 / duration if duration > 0 else float('inf')
                    logger.info(f"已处理 {processed_count} 条记录，速率: {rate:.0f} 条/秒，差异: {diff_count}")

                    # 调用reporter的进度回调（如果支持）
                    if hasattr(reporter, 'report_match'):
                        reporter.report_match(f"progress_{processed_count}", processed_count, diff_count)

                    last_log_time = current_time

                if record_a and (record_b is None or record_a.key < record_b.key):
                    # 处理A中独有的记录
                    total_records_a += 1
                    diff = DiffResult(key=record_a.key, value_a=record_a.value, status='IN_A_ONLY')
                    reporter.report_diff(diff)
                    diff_count += 1
                    record_a = next(iter_a, None)

                elif record_b and (record_a is None or record_b.key < record_a.key):
                    # 处理B中独有的记录
                    total_records_b += 1
                    diff = DiffResult(key=record_b.key, value_b=record_b.value, status='IN_B_ONLY')
                    reporter.report_diff(diff)
                    diff_count += 1
                    record_b = next(iter_b, None)

                elif record_a and record_b:  # Keys are equal
                    # 处理键相同的记录
                    total_records_a += 1
                    total_records_b += 1

                    if not compare_values(record_a.value, record_b.value):
                        diff = DiffResult(key=record_a.key, value_a=record_a.value, value_b=record_b.value, status='DIFFERENT')
                        reporter.report_diff(diff)
                        diff_count += 1

                    record_a = next(iter_a, None)
                    record_b = next(iter_b, None)

    except Exception as e:
        logger.error(f"比对过程中发生错误: {e}", exc_info=True)
        raise
    finally:
        end_time = time.perf_counter()
        total_time = end_time - start_time
        total_records_processed = total_records_a + total_records_b
        overall_rate = total_records_processed / total_time if total_time > 0 else 0
        
        logger.info("数据比对完成")
        logger.info(f"总耗时: {total_time:.2f}秒，处理记录: {total_records_processed:,}条，速率: {overall_rate:.0f}条/秒")
        logger.info(f"A源: {total_records_a:,}条，B源: {total_records_b:,}条，差异: {diff_count:,}条")

def compare_sources_memory_dict(source_a: BaseConnector, source_b: BaseConnector, reporter: BaseReporter):
    """
    关键：使用共享字典，完整加载后进行比对
    """
    logger.info("开始进行数据比对（内存字典模式）...")
    start_time = time.perf_counter()

    # 初始化变量以避免UnboundLocalError
    total_records_a = 0
    total_records_b = 0
    load_a_time = 0.0
    load_b_time = 0.0
    report_time = 0.0
    diff_count = 0

    try:
        with source_a, source_b, reporter:
            # 阶段1：加载数据源A到共享字典
            logger.info("加载数据源A...")
            load_start = time.perf_counter()

            # 创建共享字典
            data_result = source_a.fetch_all_data(shared_dict=None, data_source='t1')
            total_records_a = len(data_result)

            load_a_time = time.perf_counter() - load_start
            logger.info(f"数据源A加载完成: {total_records_a:,} 条记录，耗时: {load_a_time:.2f}秒")

            # 调用进度回调 - 数据源A加载完成
            if hasattr(reporter, 'report_match'):
                reporter.report_match("load_a_complete", total_records_a, 0)

            # 阶段2：加载数据源B到同一个字典
            logger.info("加载数据源B...")
            load_start = time.perf_counter()

            # 使用共享字典加载数据源B
            data_result = source_b.fetch_all_data(shared_dict=data_result, data_source='t2')
            total_records_b = sum(1 for item in data_result.values() if item['t2'] != '')

            load_b_time = time.perf_counter() - load_start
            logger.info(f"数据源B加载完成: {total_records_b:,} 条记录，耗时: {load_b_time:.2f}秒")

            # 调用进度回调 - 数据源B加载完成
            if hasattr(reporter, 'report_match'):
                reporter.report_match("load_b_complete", total_records_a + total_records_b, 0)

            # 阶段3：报告差异（比对已在数据加载时完成）
            logger.info("报告差异结果...")
            report_start = time.perf_counter()
            diff_count = len(data_result)  # 字典中只包含有差异的记录
            processed = 0

            # 遍历所有差异记录进行报告
            for key, item in data_result.items():
                processed += 1

                # 确定差异类型
                if item["t1"] == "":
                    status = 'IN_B_ONLY'
                    value_a = None
                    value_b = item["t2"]
                elif item["t2"] == "":
                    status = 'IN_A_ONLY'
                    value_a = item["t1"]
                    value_b = None
                else:
                    status = 'DIFFERENT'
                    value_a = item["t1"]
                    value_b = item["t2"]

                # 报告差异
                diff = DiffResult(
                    key=key,
                    value_a=value_a,
                    value_b=value_b,
                    status=status
                )
                reporter.report_diff(diff)

                # 定期输出进度
                if processed % 100000 == 0:
                    logger.info(f"已报告 {processed:,}/{diff_count:,} 条差异记录")

                    # 调用进度回调
                    if hasattr(reporter, 'report_match'):
                        reporter.report_match(f"report_progress_{processed}", processed, processed)

            report_time = time.perf_counter() - report_start
            logger.info(f"差异报告完成，耗时: {report_time:.2f}秒")

    except Exception as e:
        logger.error(f"比对过程中发生错误: {e}", exc_info=True)
        raise
    finally:
        # 输出最终统计
        end_time = time.perf_counter()
        total_time = end_time - start_time

        logger.info("数据比对完成")
        logger.info(f"总耗时: {total_time:.2f}秒，处理记录: {total_records_a + total_records_b:,}条")
        logger.info(f"A源: {total_records_a:,}条，B源: {total_records_b:,}条，差异: {diff_count:,}条")
        logger.info(f"时间分布 - 加载A: {load_a_time:.2f}s, 加载B: {load_b_time:.2f}s, 报告: {report_time:.2f}s")

