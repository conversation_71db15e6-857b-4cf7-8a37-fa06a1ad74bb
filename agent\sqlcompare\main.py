# main.py
import os
import logging
from utils.config_manager import SmartConfigManager
from connectors.db2_connector import DB2Connector
from reporters.sqlite_reporter import SqliteReporter
from core.engine import compare_sources, compare_sources_memory_dict

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(name)s] - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数，协调整个比对过程。"""
    # 1. 加载配置
    config_manager = SmartConfigManager()
    config_files = config_manager.auto_discover_configs()
    if not config_files:
        logger.error("未找到配置文件，程序退出。")
        return
    if not config_manager.load_config(config_files[0]):
        logger.error("无法加载配置，程序退出。")
        return

    # 2. 从配置中提取数据库和报告器信息
    try:
        db1_config = dict(config_manager.config['DB1'])
        db2_config = dict(config_manager.config['DB2'])

        # 获取比对类型配置
        common_config = dict(config_manager.config['COMMON'])
        cmp_type = int(common_config.get('cmp_type', 1))  # 默认为1

        # 假设我们只比对第一个表格规则
        table_rule = config_manager.rules.find('table')
        if table_rule is None:
            logger.error("在规则文件中找不到任何 'table' 规则。")
            return

        sql1 = table_rule.find('sql_1').text.strip()
        sql2 = table_rule.find('sql_2').text.strip()

        # 报告器配置
        reporter_config = {
            'db_path': 'sqlcompare_v4.db',
            'task_id': 'main_task_001',
            'batch_size': 10000
        }

    except (KeyError, AttributeError) as e:
        logger.error(f"配置文件格式错误: {e}")
        return
    except ValueError as e:
        logger.error(f"cmp_type配置值无效，必须为数字: {e}")
        return

    # 3. 实例化连接器和报告器（优化批量读取性能）
    # 批量读取大小：平衡内存使用和网络性能
    fetch_batch_size = 10000  # 10K批次，适合大数据量处理
    source_a = DB2Connector(db1_config, query=sql1, batch_size=fetch_batch_size)
    source_b = DB2Connector(db2_config, query=sql2, batch_size=fetch_batch_size)

    # SQLite报告器配置
    reporter = SqliteReporter(reporter_config)

    # 4. 根据配置选择比对方法并执行
    try:
        if cmp_type == 1:
            # 使用流式归并算法（适合超大数据集，内存占用低）
            logger.info("开始执行流式归并比对...")
            compare_sources(source_a, source_b, reporter)
        elif cmp_type == 2:
            # 使用内存字典算法（适合中等数据集，速度更快）
            logger.info("开始执行内存字典比对...")
            compare_sources_memory_dict(source_a, source_b, reporter)
        else:
            logger.error(f"不支持的比对类型: {cmp_type}，支持的类型: 1(流式归并), 2(内存字典)")
            return

        logger.info(f"比对完成，报告已生成在: {reporter_config['db_path']}")
    except Exception as e:
        logger.error(f"比对过程中发生严重错误: {e}", exc_info=True)

if __name__ == '__main__':
    main()