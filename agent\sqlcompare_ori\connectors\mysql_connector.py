#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库连接器
基于pymysql驱动，支持高性能数据读取和连接管理
"""
import time
import logging
import threading
from typing import Dict, Any, Iterator
try:
    import pymysql
    import pymysql.cursors
except ImportError:
    pymysql = None

from .base_connector import BaseConnector

logger = logging.getLogger(__name__)


class MySQLConnectionPool:
    """MySQL连接池管理器（基于pymysql）"""

    def __init__(self):
        self._pools = {}
        self._lock = threading.Lock()
        self._connections = {}

    def get_connection(self, db_config: Dict[str, Any]) -> Any:
        """从连接池获取连接"""
        key = self._get_connection_key(db_config)

        with self._lock:
            if key not in self._connections:
                self._connections[key] = []

            # 尝试获取可用连接
            while self._connections[key]:
                conn = self._connections[key].pop()
                try:
                    # 测试连接是否有效
                    conn.ping(reconnect=True)
                    return conn
                except:
                    # 连接无效，继续尝试下一个
                    continue

            # 没有可用连接，创建新连接
            return self._create_connection(db_config)

    def return_connection(self, db_config: Dict[str, Any], conn: Any):
        """将连接返回到连接池"""
        key = self._get_connection_key(db_config)

        with self._lock:
            if key not in self._connections:
                self._connections[key] = []

            # 限制连接池大小
            if len(self._connections[key]) < 5:
                self._connections[key].append(conn)
            else:
                try:
                    conn.close()
                except:
                    pass

    def _get_connection_key(self, db_config: Dict[str, Any]) -> str:
        """生成连接池键"""
        return f"{db_config.get('host', 'localhost')}:{db_config.get('port', 3306)}:{db_config.get('database', '')}"

    def _create_connection(self, db_config: Dict[str, Any]):
        """创建新连接"""
        try:
            return pymysql.connect(
                host=db_config.get('host', 'localhost'),
                port=db_config.get('port', 3306),
                user=db_config.get('username', ''),
                password=db_config.get('password', ''),
                database=db_config.get('database', ''),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True,
                connect_timeout=30,
                read_timeout=30,
                write_timeout=30
            )
        except Exception as e:
            logger.error(f"创建MySQL连接失败: {e}")
            raise


class MySqlConnector(BaseConnector):
    """
    MySQL数据库连接器（基于pymysql）

    特性：
    - 支持连接池管理
    - 批量数据读取优化
    - 自动重连机制
    - 性能统计
    - 字典游标支持
    """

    _connection_pool = MySQLConnectionPool()

    def __init__(self, db_config: Dict[str, Any], query: str = "", batch_size: int = 10000):
        """
        初始化MySQL连接器

        Args:
            db_config: 数据库配置字典，包含host, port, username, password, database等
            query: SQL查询语句
            batch_size: 批量读取大小
        """
        super().__init__(batch_size)

        # 检查驱动可用性
        if pymysql is None:
            raise ImportError("PyMySQL驱动未安装，请运行: pip install pymysql")

        self.db_config = db_config
        self.query = query
        self.conn = None
        self.cursor = None
        self._use_connection_pool = True

        # 性能统计
        self._stats = {
            'connection_time': 0.0,
            'query_time': 0.0,
            'fetch_time': 0.0,
            'total_fetched': 0
        }

        logger.info(f"MySQL连接器初始化完成，批量大小: {batch_size}")

    def connect(self):
        """建立到MySQL数据库的连接"""
        start_time = time.perf_counter()

        try:
            if self._use_connection_pool:
                self.conn = self._connection_pool.get_connection(self.db_config)
            else:
                self.conn = self._create_direct_connection()

            self._stats['connection_time'] += time.perf_counter() - start_time
            logger.info("MySQL数据库连接成功")

        except Exception as e:
            logger.error(f"MySQL连接失败: {e}")
            raise

    def _create_direct_connection(self):
        """创建直接连接（不使用连接池）"""
        try:
            return pymysql.connect(
                host=self.db_config.get('host', 'localhost'),
                port=self.db_config.get('port', 3306),
                user=self.db_config.get('username', ''),
                password=self.db_config.get('password', ''),
                database=self.db_config.get('database', ''),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True,
                connect_timeout=30
            )
        except Exception as e:
            logger.error(f"创建MySQL直接连接失败: {e}")
            raise

    def disconnect(self):
        """断开数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None

            if self.conn:
                if self._use_connection_pool:
                    self._connection_pool.return_connection(self.db_config, self.conn)
                else:
                    self.conn.close()
                self.conn = None

            logger.info("MySQL连接已断开")

        except Exception as e:
            logger.warning(f"断开MySQL连接时出现警告: {e}")

    def fetch_data(self, query: str = None) -> Iterator[Dict[str, Any]]:
        """
        获取数据的生成器

        Args:
            query: SQL查询语句，如果为None则使用初始化时的查询

        Yields:
            Dict[str, Any]: 数据行字典
        """
        if not self.conn:
            self.connect()

        query_to_use = query or self.query
        if not query_to_use:
            raise ValueError("未提供查询语句")

        start_time = time.perf_counter()

        try:
            # 使用字典游标
            self.cursor = self.conn.cursor()

            logger.info(f"执行MySQL查询: {query_to_use[:100]}...")
            self.cursor.execute(query_to_use)

            self._stats['query_time'] += time.perf_counter() - start_time

            # 批量获取数据
            fetch_start = time.perf_counter()

            while True:
                rows = self.cursor.fetchmany(self.batch_size)
                if not rows:
                    break

                self._stats['total_fetched'] += len(rows)

                for row in rows:
                    yield row

            self._stats['fetch_time'] += time.perf_counter() - fetch_start

            logger.info(f"MySQL数据获取完成，共获取 {self._stats['total_fetched']} 条记录")

        except Exception as e:
            logger.error(f"MySQL数据获取失败: {e}")
            raise
        finally:
            if self.cursor:
                self.cursor.close()
                self.cursor = None

    def get_database_version(self) -> str:
        """获取数据库版本信息"""
        if not self.conn:
            self.connect()

        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()['VERSION()']
            cursor.close()
            return version
        except Exception as e:
            logger.warning(f"获取MySQL版本失败: {e}")
            return "Unknown"

    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            if not self.conn:
                self.connect()

            cursor = self.conn.cursor()
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            cursor.close()
            return result is not None and result['test'] == 1

        except Exception as e:
            logger.error(f"MySQL连接测试失败: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'connector_type': 'MySQL',
            'driver': 'pymysql',
            'connection_time': round(self._stats['connection_time'], 3),
            'query_time': round(self._stats['query_time'], 3),
            'fetch_time': round(self._stats['fetch_time'], 3),
            'total_fetched': self._stats['total_fetched'],
            'batch_size': self.batch_size,
            'use_connection_pool': self._use_connection_pool
        }

    def execute_query(self, query: str) -> int:
        """
        执行SQL语句（INSERT, UPDATE, DELETE等）

        Args:
            query: 要执行的SQL语句

        Returns:
            int: 受影响的行数
        """
        if not self.conn:
            self.connect()

        try:
            cursor = self.conn.cursor()
            affected_rows = cursor.execute(query)
            cursor.close()
            logger.info(f"SQL执行成功，影响行数: {affected_rows}")
            return affected_rows
        except Exception as e:
            logger.error(f"SQL执行失败: {e}")
            raise

    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        获取表信息

        Args:
            table_name: 表名

        Returns:
            Dict: 表信息，包含列信息等
        """
        if not self.conn:
            self.connect()

        try:
            cursor = self.conn.cursor()
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            cursor.close()

            return {
                'table_name': table_name,
                'columns': columns
            }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            raise

    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()

    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        try:
            self.disconnect()
        except:
            pass


# 兼容性支持：保持旧的类名和接口
class MySQLConnector(MySqlConnector):
    """MySQLConnector别名，保持向后兼容"""
    pass


# 为了兼容旧版本的Record模式，添加一个适配方法
def _adapt_to_record_format(connector_instance, query: str):
    """
    适配旧版本的Record格式
    这个函数用于兼容需要key-value格式的旧代码
    """
    try:
        from core.models import Record

        for row_dict in connector_instance.fetch_data(query):
            # 假设查询返回的字典中有'key'和'value'字段
            if 'key' in row_dict and 'value' in row_dict:
                yield Record(key=row_dict['key'], value=row_dict['value'])
            else:
                # 如果没有标准的key-value格式，使用第一个字段作为key，其余作为value
                keys = list(row_dict.keys())
                if len(keys) >= 2:
                    yield Record(key=row_dict[keys[0]], value=row_dict[keys[1]])
                elif len(keys) == 1:
                    yield Record(key=row_dict[keys[0]], value=None)
    except ImportError:
        # 如果没有Record模型，直接返回字典
        for row_dict in connector_instance.fetch_data(query):
            yield row_dict