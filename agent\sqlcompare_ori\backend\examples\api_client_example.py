#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLCompare Backend API客户端使用示例
演示如何使用API进行完整的数据库比对流程
"""

import requests
import json
import time
import websocket
import threading
from typing import Dict, Any, Optional


class SQLCompareAPIClient:
    """SQLCompare API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token = None
        self.headers = {}
    
    def login(self, username: str, password: str) -> bool:
        """用户登录"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                json={"username": username, "password": password}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data["access_token"]
                self.headers = {"Authorization": f"Bearer {self.token}"}
                print(f"✅ 登录成功: {data['user']['username']}")
                return True
            else:
                print(f"❌ 登录失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def create_task(self, task_config: Dict[str, Any]) -> Optional[str]:
        """创建比对任务"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/tasks",
                json=task_config,
                headers=self.headers
            )
            
            if response.status_code == 200:
                data = response.json()
                task_id = data["data"]["task_id"]
                print(f"✅ 任务创建成功: {task_id}")
                return task_id
            else:
                print(f"❌ 任务创建失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 任务创建异常: {e}")
            return None
    
    def start_task(self, task_id: str) -> bool:
        """启动任务"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/tasks/{task_id}/actions",
                json={"action": "start", "reason": "开始执行比对"},
                headers=self.headers
            )
            
            if response.status_code == 200:
                print(f"✅ 任务启动成功: {task_id}")
                return True
            else:
                print(f"❌ 任务启动失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 任务启动异常: {e}")
            return False
    
    def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务进度"""
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/tasks/{task_id}/progress",
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取进度失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取进度异常: {e}")
            return None
    
    def get_task_results(self, task_id: str, page: int = 1, size: int = 20) -> Optional[Dict[str, Any]]:
        """获取比对结果"""
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/results/{task_id}",
                params={"page": page, "size": size},
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取结果失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取结果异常: {e}")
            return None
    
    def get_task_summary(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取比对摘要"""
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/results/{task_id}/summary",
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取摘要失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取摘要异常: {e}")
            return None
    
    def monitor_task_websocket(self, task_id: str, duration: int = 60):
        """使用WebSocket监控任务"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                if data.get("type") == "task_update":
                    task_data = data["data"]
                    print(f"📊 任务更新: 进度 {task_data.get('progress_percentage', 0):.1f}%, "
                          f"状态 {task_data.get('status', 'unknown')}")
                elif data.get("type") == "progress_update":
                    progress_data = data["data"]
                    print(f"📈 进度更新: {progress_data}")
            except Exception as e:
                print(f"❌ WebSocket消息处理异常: {e}")
        
        def on_error(ws, error):
            print(f"❌ WebSocket错误: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print("🔌 WebSocket连接已关闭")
        
        def on_open(ws):
            print(f"🔌 WebSocket连接已建立，监控任务: {task_id}")
        
        try:
            ws_url = f"ws://localhost:8000/api/v1/monitoring/ws/tasks/{task_id}"
            ws = websocket.WebSocketApp(
                ws_url,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close,
                on_open=on_open
            )
            
            # 在单独线程中运行WebSocket
            def run_websocket():
                ws.run_forever()
            
            ws_thread = threading.Thread(target=run_websocket)
            ws_thread.daemon = True
            ws_thread.start()
            
            # 等待指定时间
            time.sleep(duration)
            ws.close()
            
        except Exception as e:
            print(f"❌ WebSocket监控异常: {e}")


def main():
    """主函数 - 演示完整的API使用流程"""
    print("🚀 SQLCompare Backend API使用示例")
    print("=" * 60)
    
    # 1. 创建API客户端
    client = SQLCompareAPIClient()
    
    # 2. 用户登录
    print("\n📝 步骤1: 用户登录")
    if not client.login("admin", "admin123"):
        print("❌ 登录失败，退出程序")
        return
    
    # 3. 创建比对任务
    print("\n📝 步骤2: 创建比对任务")
    task_config = {
        "name": "API示例比对任务",
        "description": "通过API创建的示例比对任务",
        "priority": 5,
        "batch_size": 50000,
        "timeout_seconds": 3600,
        "source_conn": {
            "db_type": "db2",
            "host": "prod-db.company.com",
            "port": 50000,
            "username": "dbuser",
            "password": "password123",
            "database": "PRODDB"
        },
        "target_conn": {
            "db_type": "mysql",
            "host": "test-mysql.company.com",
            "port": 3306,
            "username": "testuser",
            "password": "password123",
            "database": "testdb"
        },
        "comparison_rules": [
            {
                "table_id": "users",
                "sql_1": "SELECT USER_ID, USERNAME, EMAIL, PHONE FROM USER_TABLE ORDER BY USER_ID",
                "sql_2": "SELECT USER_ID, USERNAME, EMAIL, PHONE FROM USER_TABLE ORDER BY USER_ID",
                "primary_keys": ["USER_ID"],
                "ignore_fields": ["LAST_UPDATE_TIME"]
            },
            {
                "table_id": "orders",
                "sql_1": "SELECT ORDER_ID, USER_ID, PRODUCT_ID, QUANTITY, PRICE FROM ORDER_TABLE ORDER BY ORDER_ID",
                "sql_2": "SELECT ORDER_ID, USER_ID, PRODUCT_ID, QUANTITY, PRICE FROM ORDER_TABLE ORDER BY ORDER_ID",
                "primary_keys": ["ORDER_ID"]
            }
        ]
    }
    
    task_id = client.create_task(task_config)
    if not task_id:
        print("❌ 任务创建失败，退出程序")
        return
    
    # 4. 启动任务
    print("\n📝 步骤3: 启动比对任务")
    if not client.start_task(task_id):
        print("❌ 任务启动失败，退出程序")
        return
    
    # 5. 监控任务进度
    print("\n📝 步骤4: 监控任务进度")
    print("使用轮询方式监控进度...")
    
    max_wait_time = 120  # 最大等待2分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        progress = client.get_task_progress(task_id)
        if progress:
            status = progress.get("status", "unknown")
            progress_pct = progress.get("progress_percentage", 0)
            current_step = progress.get("current_step", "")
            
            print(f"📊 任务状态: {status}, 进度: {progress_pct:.1f}%, 当前步骤: {current_step}")
            
            if status in ["completed", "failed", "cancelled"]:
                break
        
        time.sleep(5)  # 每5秒查询一次
    
    # 6. 获取比对结果
    print("\n📝 步骤5: 获取比对结果")
    
    # 获取摘要信息
    summary = client.get_task_summary(task_id)
    if summary:
        print(f"📈 比对摘要:")
        print(f"  - 总记录数: {summary.get('total_compared_records', 0):,}")
        print(f"  - 差异记录数: {summary.get('total_differences', 0):,}")
        print(f"  - 一致性: {summary.get('consistency_percentage', 0):.2f}%")
        print(f"  - 执行时间: {summary.get('execution_time_seconds', 0):.2f}秒")
    
    # 获取详细结果
    results = client.get_task_results(task_id, page=1, size=10)
    if results:
        items = results.get("items", [])
        pagination = results.get("pagination", {})
        
        print(f"\n📋 比对结果 (前10条):")
        print(f"总计: {pagination.get('total', 0)} 条差异记录")
        
        for i, item in enumerate(items, 1):
            print(f"  {i}. 表: {item.get('table_name')}, "
                  f"记录: {item.get('record_key')}, "
                  f"类型: {item.get('difference_type')}")
    
    # 7. WebSocket实时监控演示
    print("\n📝 步骤6: WebSocket实时监控演示")
    print("启动WebSocket监控10秒...")
    client.monitor_task_websocket(task_id, duration=10)
    
    print("\n🎉 API使用示例完成！")
    print(f"任务ID: {task_id}")
    print("您可以通过以下方式继续操作:")
    print(f"- 查看详细结果: GET /api/v1/results/{task_id}")
    print(f"- 导出结果: POST /api/v1/results/{task_id}/export")
    print(f"- 查看任务详情: GET /api/v1/tasks/{task_id}")


def test_api_endpoints():
    """测试各个API端点"""
    print("\n🧪 API端点测试")
    print("=" * 60)
    
    client = SQLCompareAPIClient()
    
    # 测试健康检查
    try:
        response = requests.get(f"{client.base_url}/health")
        if response.status_code == 200:
            print("✅ 健康检查: 正常")
        else:
            print("❌ 健康检查: 异常")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 测试API文档
    try:
        response = requests.get(f"{client.base_url}/docs")
        if response.status_code == 200:
            print("✅ API文档: 可访问")
            print(f"   访问地址: {client.base_url}/docs")
        else:
            print("❌ API文档: 不可访问")
    except Exception as e:
        print(f"❌ API文档异常: {e}")
    
    # 测试系统指标
    try:
        response = requests.get(f"{client.base_url}/api/v1/monitoring/metrics")
        if response.status_code == 200:
            print("✅ 系统指标: 正常")
        else:
            print("❌ 系统指标: 异常")
    except Exception as e:
        print(f"❌ 系统指标异常: {e}")


if __name__ == "__main__":
    # 首先测试API端点
    test_api_endpoints()
    
    # 然后运行完整示例
    main()
