"""
SQLAlchemy业务逻辑层
本模块专注于业务逻辑实现，实现关注点分离的架构设计。
"""
import sys
import uuid
import time
import base64
import logging
import threading
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from sqlalchemy.orm import joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, inspect, desc, func, text

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
from models.pydantic_models import TaskExecutionInfo
from models.sqlalchemy_models import (
    User, UserSession, ComparisonConnection, ComparisonModel,
    ComparisonTableRule, ComparisonTask, ComparisonResult,
    TaskStatus, DifferenceStatus, build_conn_engine,
    create_database_engine, create_tables, get_session_factory
)

logger = logging.getLogger(__name__)


class SQLAlchemyComparisonService:
    """SQLAlchemy比对服务 - 单例模式"""
    _instance = None
    _instance_url = None
    _lock = threading.Lock()

    def __init__(self, database_url: str = None):
        # 避免重复初始化，如果已初始化则直接返回
        if hasattr(self, '_initialized'):
            return

        if database_url is None:
            database_url = self._get_default_database_url()

        # 存储数据库URL用于调试和日志
        self.database_url = database_url
        self.database_type = self._detect_database_type(database_url)

        self.engine = create_database_engine(database_url)
        self.SessionLocal = get_session_factory(self.engine)
        self._ensure_tables_initialized()

        # 初始化和进度跟踪控制
        self._initialized = True
        self._last_progress_update = {}
        self._progress_update_interval = 3.0

        logger.info(f"SQLAlchemy服务初始化完成: {database_url}")

    def _init_high_performance_writer(self):
        """初始化高性能写入组件"""
        try:
            # 只对SQLite数据库启用高性能写入
            if self.database_type == 'sqlite':
                # 从SQLAlchemy引擎URL提取数据库路径
                db_url = str(self.engine.url)
                if db_url.startswith('sqlite:///'):
                    self.sqlite_db_path = db_url[10:]  # 移除 'sqlite:///' 前缀

                    # 创建高性能SQLite连接
                    self.high_perf_conn = sqlite3.connect(
                        self.sqlite_db_path,
                        check_same_thread=False,
                        timeout=30
                    )
                    self.high_perf_cursor = self.high_perf_conn.cursor()

                    # 应用高性能PRAGMA设置
                    self._apply_high_performance_pragma()

                    # 预编译常用的插入语句
                    self._prepare_insert_statements()

                    self.high_perf_enabled = True
                    logger.info("高性能SQLite写入组件初始化成功")
                else:
                    self.high_perf_enabled = False
            else:
                self.high_perf_enabled = False
                logger.info(f"数据库类型 {self.database_type} 不支持高性能写入优化")

        except Exception as e:
            logger.warning(f"高性能写入组件初始化失败: {e}")
            self.high_perf_enabled = False

    def _apply_high_performance_pragma(self):
        """应用高性能PRAGMA设置"""
        # 基础高性能设置（不影响并发）
        base_pragma_settings = [
            "PRAGMA journal_mode=WAL",         # WAL模式，平衡性能和并发
            "PRAGMA synchronous=NORMAL",       # 平衡性能和安全性
            "PRAGMA cache_size=100000",        # 大幅增大缓存
            "PRAGMA temp_store=MEMORY",        # 临时数据存储在内存
            "PRAGMA count_changes=OFF",        # 关闭计数
            "PRAGMA auto_vacuum=NONE"          # 关闭自动清理
        ]

        # 检查是否需要极致性能模式（可能影响并发）
        extreme_performance = getattr(self, 'extreme_performance_mode', False)

        if extreme_performance:
            # 极致性能模式：牺牲并发性换取最大性能
            extreme_pragma_settings = [
                "PRAGMA journal_mode=MEMORY",      # 内存模式，最快但影响并发
                "PRAGMA synchronous=OFF",          # 关闭同步，最大性能但有风险
                "PRAGMA locking_mode=EXCLUSIVE",   # 独占锁，最快但阻塞其他连接
            ]
            pragma_settings = extreme_pragma_settings + [
                "PRAGMA cache_size=100000",
                "PRAGMA temp_store=MEMORY",
                "PRAGMA count_changes=OFF",
                "PRAGMA auto_vacuum=NONE"
            ]
            logger.warning("启用极致性能模式：可能影响SQLAlchemy并发访问")
        else:
            # 平衡模式：保持并发性的高性能设置
            pragma_settings = base_pragma_settings
            logger.info("启用平衡性能模式：保持SQLAlchemy并发访问能力")

        for pragma in pragma_settings:
            self.high_perf_conn.execute(pragma)

        # 记录当前锁模式
        cursor = self.high_perf_conn.cursor()
        cursor.execute("PRAGMA locking_mode")
        lock_mode = cursor.fetchone()[0]
        self.current_lock_mode = lock_mode

        logger.debug(f"高性能PRAGMA设置已应用，锁模式: {lock_mode}")

    def enable_extreme_performance_mode(self, enable: bool = True):
        """启用/禁用极致性能模式"""
        if not self.high_perf_enabled:
            logger.warning("高性能组件未启用，无法切换极致性能模式")
            return False

        try:
            if enable and not getattr(self, 'extreme_performance_mode', False):
                # 切换到极致性能模式
                self.high_perf_conn.execute("PRAGMA journal_mode=MEMORY")
                self.high_perf_conn.execute("PRAGMA synchronous=OFF")
                self.high_perf_conn.execute("PRAGMA locking_mode=EXCLUSIVE")
                self.extreme_performance_mode = True
                logger.warning("已启用极致性能模式：SQLAlchemy并发访问将被阻塞")

            elif not enable and getattr(self, 'extreme_performance_mode', False):
                # 切换回平衡模式
                self.high_perf_conn.execute("PRAGMA locking_mode=NORMAL")
                self.high_perf_conn.execute("PRAGMA journal_mode=WAL")
                self.high_perf_conn.execute("PRAGMA synchronous=NORMAL")
                self.extreme_performance_mode = False
                logger.info("已切换回平衡模式：恢复SQLAlchemy并发访问")

            # 更新锁模式状态
            cursor = self.high_perf_conn.cursor()
            cursor.execute("PRAGMA locking_mode")
            self.current_lock_mode = cursor.fetchone()[0]

            return True

        except Exception as e:
            logger.error(f"切换性能模式失败: {e}")
            return False

    def get_lock_mode_status(self) -> Dict[str, Any]:
        """获取当前锁模式状态"""
        if not self.high_perf_enabled:
            return {'high_perf_enabled': False}

        try:
            cursor = self.high_perf_conn.cursor()

            # 获取各种模式状态
            cursor.execute("PRAGMA locking_mode")
            lock_mode = cursor.fetchone()[0]

            cursor.execute("PRAGMA journal_mode")
            journal_mode = cursor.fetchone()[0]

            cursor.execute("PRAGMA synchronous")
            synchronous = cursor.fetchone()[0]

            return {
                'high_perf_enabled': True,
                'lock_mode': lock_mode,
                'journal_mode': journal_mode,
                'synchronous': synchronous,
                'extreme_performance_mode': getattr(self, 'extreme_performance_mode', False),
                'sqlalchemy_concurrent_safe': lock_mode.upper() != 'EXCLUSIVE'
            }

        except Exception as e:
            logger.error(f"获取锁模式状态失败: {e}")
            return {'error': str(e)}

    def _prepare_insert_statements(self):
        """预编译常用的插入语句"""
        # 比对结果插入语句
        self.insert_result_sql = """
            INSERT INTO comparison_results
            (task_id, table_name, record_key, status, field_name, source_value, target_value, diff_type, partition_key)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # 任务状态更新语句
        self.update_task_sql = """
            UPDATE comparison_tasks
            SET status = ?, progress_pct = ?, current_step = ?, processed_records = ?,
                diff_records = ?, update_time = ?
            WHERE task_id = ?
        """

        logger.debug("插入语句预编译完成")

    @classmethod
    def get_instance(cls, reporter_config: Dict[str, Any] = None) -> 'SQLAlchemyComparisonService':
        """获取单例实例"""
        database_url = cls._get_database_url_from_config(reporter_config)
        if cls._instance and cls._instance_url == database_url:
            return cls._instance

        with cls._lock:
            if cls._instance is None or cls._instance_url != database_url:
                cls._instance = cls(database_url)
                cls._instance_url = database_url
        
        return cls._instance

    @staticmethod
    def _get_database_url_from_config(reporter_config: Dict[str, Any] = None) -> str:
        """从报告器配置中提取或生成数据库URL"""
        if reporter_config and 'configurations' in reporter_config:
            # 优先查找PostgreSQL配置
            if 'postgres' in reporter_config['configurations']:
                pg_config = reporter_config['configurations']['postgres']
                user = pg_config.get('user', pg_config.get('username'))
                dbname = pg_config.get('dbname', pg_config.get('database'))
                password = pg_config.get('password', '')
                host = pg_config.get('host', 'localhost')
                port = pg_config.get('port', 5432)
                return f"postgresql+psycopg2://{user}:{password}@{host}:{port}/{dbname}"
            # 其次查找SQLite配置
            elif 'sqlite' in reporter_config['configurations']:
                sqlite_config = reporter_config['configurations']['sqlite']
                return f"sqlite:///{sqlite_config.get('db_path', 'sqlcompare_v4.db')}"
        
        # 如果配置中没有，则回退到默认URL
        return SQLAlchemyComparisonService._get_default_database_url()

    @staticmethod
    def _get_default_database_url() -> str:
        default_url = "sqlite:///sqlcompare_v4.db"
        return default_url

    @staticmethod
    def _detect_database_type(database_url: str) -> str:
        """检测数据库类型"""
        if database_url.startswith('postgresql'):
            return 'postgresql'
        elif database_url.startswith('mysql'):
            return 'mysql'
        elif database_url.startswith('sqlite'):
            return 'sqlite'
        elif database_url.startswith('oracle'):
            return 'oracle'
        elif database_url.startswith('db2'):
            return 'db2'
        else:
            return 'unknown'

    def _ensure_tables_initialized(self):
        """确保数据库表已初始化"""
        try:           
            inspector = inspect(self.engine)
            existing_tables = inspector.get_table_names()

            # 检查关键表是否存在
            required_tables = ['comparison_models', 'comparison_results', 'comparison_table_rules', 'comparison_tasks']
            tables_initialized = all(table in existing_tables for table in required_tables)

            if not tables_initialized:
                create_tables(self.engine)

        except Exception as e:
            logger.error(f"数据库表初始化失败: {e}")
            raise

    def get_task_details(self, task_id: str) -> Optional[ComparisonTask]:
        """获取任务详细信息"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter(ComparisonTask.task_id == task_id).first()

            if task:
                # 使用expunge将对象从会话中分离，但保留已加载的属性
                session.expunge(task)

            return task

    def get_task_execution_info(self, task_id: str):
        """获取任务执行信息"""
        if not task_id:
            raise ValueError("任务ID不能为空")

        try:
            query_start_time = time.perf_counter()
            with self.get_db_session() as session:
                # 一次性预加载所有相关数据，避免N+1查询问题
                task = session.query(ComparisonTask).options(
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.source_conn, innerjoin=False),
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.target_conn, innerjoin=False),
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.table_rules, innerjoin=False)
                ).filter(ComparisonTask.task_id == task_id).first()

                query_time = time.perf_counter() - query_start_time
                logger.debug(f"任务执行信息查询耗时: {query_time:.3f}秒 (任务ID: {task_id})")

                if not task or not task.model:
                    logger.warning(f"获取任务执行信息不存在: {task_id}")
                    return None

                # 处理表规则信息
                table_rules = []
                if hasattr(task.model, 'table_rules') and task.model.table_rules:
                    for rule in task.model.table_rules:
                        if rule.is_active:
                            table_rules.append({
                                'table_name': rule.table_name,
                                'remark': rule.remark,
                                'source_sql': rule.sql_1,
                                'target_sql': rule.sql_2
                            })

                # 计算匹配记录数
                matched_records = max(0, (task.total_records or 0) - (task.diff_records or 0) - (task.source_only or 0) - (task.target_only or 0))

                # 计算执行持续时间
                duration = None
                if task.start_time and task.complete_time:
                    duration = (task.complete_time - task.start_time).total_seconds()
                elif task.start_time and not task.complete_time and task.status == TaskStatus.RUNNING.value:
                    duration = (datetime.now() - task.start_time).total_seconds()

                # 标准模式：从关联表获取配置
                config_summary = {
                    'model_name': task.model.name,
                    'comparison_type': task.model.cmp_type,
                    'source_conn_name': task.model.source_conn.name if task.model.source_conn else None,
                    'target_conn_name': task.model.target_conn.name if task.model.target_conn else None,
                    'global_config': task.model.global_config
                }

                # 构建任务执行信息               
                execution_info = TaskExecutionInfo(
                    task_id=task.task_id,
                    task_name=task.task_name,
                    user_id=task.user_id,
                    model_id=task.model_id,
                    status=task.status,
                    progress_pct=task.progress_pct or 0,
                    current_step=task.current_step,
                    create_time=task.create_time,
                    start_time=task.start_time,
                    complete_time=task.complete_time,
                    update_time=task.update_time,
                    duration=duration,
                    total_records=task.total_records or 0,
                    processed_records=task.processed_records or 0,
                    matched_records=matched_records,
                    diff_records=task.diff_records or 0,
                    source_only=task.source_only or 0,
                    target_only=task.target_only or 0,
                    exec_time=task.exec_time or 0,
                    error_msg=task.error_msg,
                    error_details=task.error_details,
                    config_summary=None,
                    model=task.model,
                    table_rules=table_rules
                )

                logger.info(f"成功获取任务执行信息: {task_id}")
                return execution_info

        except SQLAlchemyError as e:
            logger.error(f"获取任务执行信息时数据库错误: {e}")
            raise
        except Exception as e:
            logger.error(f"获取任务执行信息时发生未知错误: {e}")
            raise

    @contextmanager
    def get_db_session(self):
        """数据库会话上下文管理器"""

        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        except Exception as e:
            session.rollback()
            logger.error(f"未知错误: {e}")
            raise
        finally:
            session.close()

    # ==================== 用户管理 ====================

    def create_user(self, username: str, email: str, password: str, role: str = 'user') -> str:
        """创建用户"""
        with self.get_db_session() as session:
            # 生成唯一的user_id
            user_id = f"u_{hash(username) % 1000000:06d}"

            user = User(
                user_id=user_id,
                username=username,
                email=email,
                password=password,
                role=role
            )
            session.add(user)
            session.flush()
            return user.user_id
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """用户认证"""
        with self.get_db_session() as session:
            user = session.query(User).filter(
                and_(User.email == email, User.is_active == True)
            ).first()
            
            if user and user.password == password:
                # 更新最后登录时间
                user.last_login_at = datetime.now()
                return user
            return None
    
    def create_user_session(self, user_id: str, ip_address: str, user_agent: str) -> str:
        """创建用户会话"""
        with self.get_db_session() as session:
            session_id = str(uuid.uuid4())
            expires_at = datetime.now() + timedelta(hours=24)  # 24小时过期
            
            user_session = UserSession(
                session_id=session_id,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=expires_at
            )
            session.add(user_session)
            return session_id
    
    # ==================== 连接管理 ====================
    
    def create_database_connection(self, name: str, type: str, host: str,
                                 port: int, username: str, password: str,
                                 database: str, **kwargs) -> int:
        """创建数据库连接配置"""
        # 输入验证
        if not all([name, type, host, username, password, database]):
            raise ValueError("所有必需参数都不能为空")
        if port <= 0 or port > 65535:
            raise ValueError("端口号必须在1-65535范围内")

        with self.get_db_session() as session:
            # 检查连接名称是否已存在
            existing = session.query(ComparisonConnection).filter_by(name=name).first()
            if existing:
                raise ValueError(f"连接名称 '{name}' 已存在")

            connection = ComparisonConnection(
                name=name,
                type=type.lower(),
                host=host,
                port=port,
                username=username,
                password=base64.b64encode(password.encode('utf-8')).decode('utf-8'),
                database=database,
                params=kwargs.get('params')
            )
            session.add(connection)
            session.flush()

            return connection.id
    
    def get_connection_by_name(self, name: str) -> Optional[ComparisonConnection]:
        """ 根据名称查询数据库连接 """
        with self.get_db_session() as session:
            connection = session.query(ComparisonConnection).filter_by(name=name).first()
            if connection:
                session.expunge(connection)
                # 解密密码
                if connection.password:
                    connection.password = base64.b64decode(connection.password).decode('utf-8')
            return connection

    def test_database_connection(self, connection_id: int) -> Dict[str, Any]:
        """ 使用SQLAlchemy直接测试数据库连接"""
        with self.get_db_session() as session:
            connection = session.query(ComparisonConnection).filter_by(id=connection_id).first()
            if not connection:
                return {'success': False, 'error': f'连接配置不存在: {connection_id}'}

            try:
                from sqlalchemy import text
                from sqlalchemy.exc import SQLAlchemyError
                
                test_success = False
                error_message = None
                start_time = time.time()

                # 构建数据库连接引擎
                engine = build_conn_engine(
                    db_type=connection.type,
                    host=connection.host,
                    port=connection.port,
                    username=connection.username,
                    password=base64.b64decode(connection.password),
                    database=connection.database
                )
                if not engine:
                    return { 'success': False, 'error': '不支持的数据库类型', 'connection_id': connection_id}

                try:
                    # 获取连接并执行测试查询
                    with engine.connect() as conn:
                        test_query = self._get_test_query(connection.type)
                        result = conn.execute(text(test_query))

                        row = result.fetchone()
                        if row is not None:
                            test_success = True
                        else:
                            error_message = "测试查询未返回结果"

                except SQLAlchemyError as e:
                    error_message = f"SQLAlchemy错误: {str(e)}"
                    logger.error(f"数据库连接测试失败 [{connection.name}]: {error_message}")

                except Exception as e:
                    error_message = f"连接错误: {str(e)}"
                    logger.error(f"数据库连接测试异常 [{connection.name}]: {error_message}")

                finally:
                    # 确保引擎资源被释放
                    try:
                        engine.dispose()
                    except Exception as e:
                        logger.warning(f"释放数据库引擎资源时出错: {e}")

                response_time = time.time() - start_time
                # 更新连接状态
                connection.status = 'active' if test_success else 'error'

                if test_success:
                    return {'success': True, 'connection_id': connection_id,'response_time': round(response_time, 3)}
                else:
                    return {'success': False, 'error': error_message or '未知连接错误', 'connection_id': connection_id}

            except Exception as e:
                connection.status = 'error'
                error_msg = f"连接测试异常: {str(e)}"
                return {'success': False, 'error': error_msg, 'connection_id': connection_id}

    def _get_test_query(self, db_type: str) -> str:
        """获取数据库测试查询语句"""
        db_type_lower = db_type.lower()

        if db_type_lower == 'mysql':
            return "SELECT 1 as test_result"
        elif db_type_lower == 'db2':
            return "SELECT 1 as test_result FROM SYSIBM.SYSDUMMY1"
        elif db_type_lower in ['postgresql', 'postgres']:
            return "SELECT 1 as test_result"
        elif db_type_lower == 'sqlite':
            return "SELECT 1 as test_result"
        elif db_type_lower in ['oracle', 'ora']:
            return "SELECT 1 as test_result FROM DUAL"
        elif db_type_lower in ['sqlserver', 'mssql']:
            return "SELECT 1 as test_result"
        else:
            return "SELECT 1 as test_result"

    def _get_or_create_user(self, session, user_name: str) -> User:
        """获取或创建用户"""
        user = session.query(User).filter_by(username=user_name).first()
        if user:
            return user

        user_id = f"U{uuid.uuid4().hex[:8].upper()}"
        new_user = User(
            user_id=user_id,
            username=user_name,
            email=f"{user_name}@example.com",
            password="auto_created_password" 
        )
        session.add(new_user)
        session.flush()
        logger.info(f"成功创建新用户: {user_name} (ID: {user_id})")
        return new_user
    
    # ==================== 结果管理 ====================
    
    def save_comparison_results(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """批量保存比对结果 - 智能选择最优SQLAlchemy方法"""
        if not results:
            return 0

        # 根据数据量智能选择最优的SQLAlchemy方法
        if len(results) > 10000:
            # 超大批量：使用DBAPI原生接口
            return self.save_results_sqlalchemy_dbapi_raw(task_id, results)
        elif len(results) > 1000:
            # 大批量：使用SQLAlchemy Core优化
            return self.save_results_sqlalchemy_optimized(task_id, results)
        else:
            # 小批量：使用标准方法
            return self._save_results_standard(task_id, results)

    def _save_results_high_performance(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """高性能批量插入比对结果"""
        try:
            # 验证任务存在（使用SQLAlchemy查询）
            with self.get_db_session() as session:
                task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
                if not task:
                    raise ValueError(f"任务 {task_id} 不存在")

            # 准备批量数据
            batch_data = []
            for result_data in results:
                batch_data.append((
                    task_id,
                    result_data.get('table_name'),
                    result_data.get('record_key'),
                    result_data.get('status', 'new'),
                    result_data.get('field_name'),
                    result_data.get('source_value'),
                    result_data.get('target_value'),
                    result_data.get('diff_type'),
                    result_data.get('partition_key')
                ))

            # 智能锁模式管理
            use_extreme_mode = len(results) > 10000  # 大于1万条记录使用极致模式
            original_extreme_mode = getattr(self, 'extreme_performance_mode', False)

            if use_extreme_mode and not original_extreme_mode:
                logger.info(f"大批量数据({len(results)}条)，临时启用极致性能模式")
                self.enable_extreme_performance_mode(True)

            try:
                # 高性能批量插入
                start_time = time.time()
                self.high_perf_cursor.executemany(self.insert_result_sql, batch_data)
                self.high_perf_conn.commit()

                elapsed_time = time.time() - start_time
                records_per_second = len(results) / elapsed_time if elapsed_time > 0 else 0

                logger.info(f"高性能插入完成: {len(results)} 条记录，耗时 {elapsed_time:.3f}秒，"
                           f"性能: {records_per_second:,.0f} 条/秒")

            finally:
                # 恢复原始锁模式
                if use_extreme_mode and not original_extreme_mode:
                    logger.info("恢复平衡性能模式，确保SQLAlchemy并发访问")
                    self.enable_extreme_performance_mode(False)

            # 使用SQLAlchemy更新任务统计
            self._update_task_statistics_sqlalchemy(task_id, len(results))

            return len(results)

        except Exception as e:
            logger.error(f"高性能插入失败: {e}")
            # 回退到标准方法
            return self._save_results_standard(task_id, results)

    def _save_results_standard(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """标准SQLAlchemy批量插入（兼容性方法）"""
        with self.get_db_session() as session:
            # 验证任务存在
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")

            # 使用bulk_insert_mappings优化性能
            session.bulk_insert_mappings(ComparisonResult, [
                {
                    'task_id': task_id,
                    'table_name': result_data.get('table_name'),
                    'record_key': result_data.get('record_key'),
                    'status': result_data.get('status', 'new'),
                    'field_name': result_data.get('field_name'),
                    'source_value': result_data.get('source_value'),
                    'target_value': result_data.get('target_value'),
                    'diff_type': result_data.get('diff_type'),
                    'partition_key': result_data.get('partition_key')
                }
                for result_data in results
            ])

            # 更新任务统计
            task.diff_records = (task.diff_records or 0) + len(results)

            return len(results)

    def save_results_sqlalchemy_optimized(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """SQLAlchemy原生优化批量插入 - 接近原生SQLite性能"""
        if not results:
            return 0

        start_time = time.time()

        try:
            # 方法1：使用SQLAlchemy Core的批量插入（最快）
            if len(results) > 5000:
                return self._bulk_insert_core_optimized(task_id, results)

            # 方法2：使用优化的bulk_insert_mappings
            elif len(results) > 1000:
                return self._bulk_insert_mappings_optimized(task_id, results)

            # 方法3：小批量使用标准方法
            else:
                return self._save_results_standard(task_id, results)

        except Exception as e:
            logger.error(f"SQLAlchemy优化插入失败: {e}")
            # 回退到标准方法
            return self._save_results_standard(task_id, results)
        finally:
            elapsed_time = time.time() - start_time
            if elapsed_time > 0:
                records_per_second = len(results) / elapsed_time
                logger.info(f"SQLAlchemy优化插入完成: {len(results)} 条记录，"
                           f"耗时 {elapsed_time:.3f}秒，性能: {records_per_second:,.0f} 条/秒")

    def _bulk_insert_core_optimized(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """使用SQLAlchemy Core进行超高性能批量插入"""
        from sqlalchemy import text

        # 验证任务存在
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")

        # 准备批量数据
        batch_data = []
        for result_data in results:
            batch_data.append({
                'task_id': task_id,
                'table_name': result_data.get('table_name'),
                'record_key': result_data.get('record_key'),
                'status': result_data.get('status', 'new'),
                'field_name': result_data.get('field_name'),
                'source_value': result_data.get('source_value'),
                'target_value': result_data.get('target_value'),
                'diff_type': result_data.get('diff_type'),
                'partition_key': result_data.get('partition_key')
            })

        # 使用SQLAlchemy Core的原生批量插入
        with self.engine.begin() as conn:
            # 获取表对象
            table = ComparisonResult.__table__

            # 执行批量插入
            conn.execute(table.insert(), batch_data)

            # 更新任务统计
            update_sql = text("""
                UPDATE comparison_tasks
                SET diff_records = COALESCE(diff_records, 0) + :count,
                    update_time = CURRENT_TIMESTAMP
                WHERE task_id = :task_id
            """)
            conn.execute(update_sql, {'count': len(results), 'task_id': task_id})

        return len(results)

    def _bulk_insert_mappings_optimized(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """优化的bulk_insert_mappings方法"""
        with self.get_db_session() as session:
            # 验证任务存在
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")

            # 准备映射数据
            mappings = [
                {
                    'task_id': task_id,
                    'table_name': result_data.get('table_name'),
                    'record_key': result_data.get('record_key'),
                    'status': result_data.get('status', 'new'),
                    'field_name': result_data.get('field_name'),
                    'source_value': result_data.get('source_value'),
                    'target_value': result_data.get('target_value'),
                    'diff_type': result_data.get('diff_type'),
                    'partition_key': result_data.get('partition_key')
                }
                for result_data in results
            ]

            # 使用bulk_insert_mappings，禁用返回默认值以提升性能
            session.bulk_insert_mappings(
                ComparisonResult,
                mappings,
                return_defaults=False,  # 关键优化：不返回默认值
                render_nulls=True       # 渲染NULL值以提升性能
            )

            # 更新任务统计
            task.diff_records = (task.diff_records or 0) + len(results)

            return len(results)

    def save_results_sqlalchemy_dbapi_raw(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """使用SQLAlchemy DBAPI原生接口进行极致性能插入"""
        if not results:
            return 0

        start_time = time.time()

        try:
            # 验证任务存在
            with self.get_db_session() as session:
                task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
                if not task:
                    raise ValueError(f"任务 {task_id} 不存在")

            # 获取原生DBAPI连接
            with self.engine.raw_connection() as raw_conn:
                cursor = raw_conn.cursor()

                # 应用临时性能优化设置
                self._apply_temp_performance_settings(cursor)

                try:
                    # 准备批量数据
                    batch_data = []
                    for result_data in results:
                        batch_data.append((
                            task_id,
                            result_data.get('table_name'),
                            result_data.get('record_key'),
                            result_data.get('status', 'new'),
                            result_data.get('field_name'),
                            result_data.get('source_value'),
                            result_data.get('target_value'),
                            result_data.get('diff_type'),
                            result_data.get('partition_key')
                        ))

                    # 原生批量插入
                    insert_sql = """
                        INSERT INTO comparison_results
                        (task_id, table_name, record_key, status, field_name, source_value, target_value, diff_type, partition_key)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """

                    cursor.executemany(insert_sql, batch_data)

                    # 更新任务统计
                    update_sql = """
                        UPDATE comparison_tasks
                        SET diff_records = COALESCE(diff_records, 0) + ?,
                            update_time = CURRENT_TIMESTAMP
                        WHERE task_id = ?
                    """
                    cursor.execute(update_sql, (len(results), task_id))

                    # 提交事务
                    raw_conn.commit()

                finally:
                    # 恢复默认设置
                    self._restore_default_settings(cursor)
                    cursor.close()

            elapsed_time = time.time() - start_time
            records_per_second = len(results) / elapsed_time if elapsed_time > 0 else 0

            logger.info(f"SQLAlchemy DBAPI原生插入完成: {len(results)} 条记录，"
                       f"耗时 {elapsed_time:.3f}秒，性能: {records_per_second:,.0f} 条/秒")

            return len(results)

        except Exception as e:
            logger.error(f"SQLAlchemy DBAPI原生插入失败: {e}")
            # 回退到标准方法
            return self._save_results_standard(task_id, results)

    def _apply_temp_performance_settings(self, cursor):
        """应用临时性能优化设置"""
        try:
            # 只对SQLite应用优化设置
            if self.database_type == 'sqlite':
                performance_pragmas = [
                    "PRAGMA synchronous=OFF",      # 临时关闭同步
                    "PRAGMA journal_mode=MEMORY",  # 临时内存模式
                    "PRAGMA temp_store=MEMORY",    # 临时存储在内存
                    "PRAGMA cache_size=50000"      # 增大缓存
                ]

                for pragma in performance_pragmas:
                    cursor.execute(pragma)

                logger.debug("临时性能优化设置已应用")
        except Exception as e:
            logger.warning(f"应用临时性能设置失败: {e}")

    def _restore_default_settings(self, cursor):
        """恢复默认设置"""
        try:
            if self.database_type == 'sqlite':
                default_pragmas = [
                    "PRAGMA synchronous=NORMAL",   # 恢复正常同步
                    "PRAGMA journal_mode=WAL",     # 恢复WAL模式
                    "PRAGMA temp_store=DEFAULT",   # 恢复默认临时存储
                    "PRAGMA cache_size=2000"       # 恢复默认缓存
                ]

                for pragma in default_pragmas:
                    cursor.execute(pragma)

                logger.debug("默认设置已恢复")
        except Exception as e:
            logger.warning(f"恢复默认设置失败: {e}")

    def _update_task_statistics_sqlalchemy(self, task_id: str, new_records_count: int):
        """使用SQLAlchemy更新任务统计"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if task:
                task.diff_records = (task.diff_records or 0) + new_records_count
                task.update_time = datetime.now()
                session.commit()

    def update_task_progress_high_performance(self, task_id: str, status: str = None,
                                            progress_pct: float = None, current_step: str = None,
                                            processed_records: int = None, diff_records: int = None) -> bool:
        """高性能任务进度更新"""
        if not self.high_perf_enabled:
            # 回退到标准SQLAlchemy方法
            return self._update_task_progress_standard(task_id, status, progress_pct,
                                                     current_step, processed_records, diff_records)

        try:
            # 构建动态更新语句
            update_fields = []
            update_values = []

            if status is not None:
                update_fields.append("status = ?")
                update_values.append(status)
            if progress_pct is not None:
                update_fields.append("progress_pct = ?")
                update_values.append(progress_pct)
            if current_step is not None:
                update_fields.append("current_step = ?")
                update_values.append(current_step)
            if processed_records is not None:
                update_fields.append("processed_records = ?")
                update_values.append(processed_records)
            if diff_records is not None:
                update_fields.append("diff_records = ?")
                update_values.append(diff_records)

            # 总是更新时间戳
            update_fields.append("update_time = ?")
            update_values.append(datetime.now())

            # 添加WHERE条件
            update_values.append(task_id)

            # 执行更新
            if update_fields:
                sql = f"UPDATE comparison_tasks SET {', '.join(update_fields)} WHERE task_id = ?"
                self.high_perf_cursor.execute(sql, update_values)
                self.high_perf_conn.commit()
                return True

            return False

        except Exception as e:
            logger.error(f"高性能任务更新失败: {e}")
            # 回退到标准方法
            return self._update_task_progress_standard(task_id, status, progress_pct,
                                                     current_step, processed_records, diff_records)

    def _update_task_progress_standard(self, task_id: str, status: str = None,
                                     progress_pct: float = None, current_step: str = None,
                                     processed_records: int = None, diff_records: int = None) -> bool:
        """标准SQLAlchemy任务进度更新"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                return False

            # 更新字段
            if status is not None:
                task.status = status
            if progress_pct is not None:
                task.progress_pct = progress_pct
            if current_step is not None:
                task.current_step = current_step
            if processed_records is not None:
                task.processed_records = processed_records
            if diff_records is not None:
                task.diff_records = diff_records

            task.update_time = datetime.now()
            session.commit()
            return True

    def batch_update_task_progress(self, updates: List[Dict[str, Any]]) -> int:
        """批量更新多个任务的进度（高性能版本）"""
        if not self.high_perf_enabled or len(updates) < 5:
            # 少量更新使用标准方法
            success_count = 0
            for update in updates:
                task_id = update.pop('task_id')
                if self._update_task_progress_standard(task_id, **update):
                    success_count += 1
            return success_count

        try:
            # 批量更新
            update_data = []
            for update in updates:
                task_id = update.get('task_id')
                if not task_id:
                    continue

                update_data.append((
                    update.get('status'),
                    update.get('progress_pct'),
                    update.get('current_step'),
                    update.get('processed_records'),
                    update.get('diff_records'),
                    datetime.now(),
                    task_id
                ))

            if update_data:
                sql = """
                    UPDATE comparison_tasks
                    SET status = COALESCE(?, status),
                        progress_pct = COALESCE(?, progress_pct),
                        current_step = COALESCE(?, current_step),
                        processed_records = COALESCE(?, processed_records),
                        diff_records = COALESCE(?, diff_records),
                        update_time = ?
                    WHERE task_id = ?
                """
                self.high_perf_cursor.executemany(sql, update_data)
                self.high_perf_conn.commit()
                return len(update_data)

            return 0

        except Exception as e:
            logger.error(f"批量更新任务进度失败: {e}")
            return 0

    def close_high_performance_connection(self):
        """关闭高性能连接"""
        if hasattr(self, 'high_perf_conn') and self.high_perf_conn:
            try:
                self.high_perf_conn.close()
                logger.info("高性能SQLite连接已关闭")
            except Exception as e:
                logger.error(f"关闭高性能连接失败: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'high_performance_enabled': getattr(self, 'high_perf_enabled', False),
            'database_type': self.database_type,
            'database_url': self.database_url,
            'sqlite_db_path': getattr(self, 'sqlite_db_path', None)
        }

    def get_comparison_results(self, task_id: str, difference_type: Optional[str] = None,
                             limit: int = 100, offset: int = 0) -> List[ComparisonResult]:
        """获取比对结果 - 展示复杂查询和分页"""
        with self.get_db_session() as session:
            query = session.query(ComparisonResult).filter_by(task_id=task_id)
            
            if difference_type:
                query = query.filter(ComparisonResult.difference_type == difference_type)
            
            results = query.order_by(ComparisonResult.created_at).offset(offset).limit(limit).all()
            return results
    
    def get_task_statistics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务统计 - 展示聚合查询"""
        with self.get_db_session() as session:
            # 使用SQLAlchemy的聚合函数
            stats = session.query(
                func.count(ComparisonResult.id).label('total_differences'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DifferenceStatus.DIFFERENT.value
                ).label('different_records'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DifferenceStatus.SOURCE_ONLY.value
                ).label('source_only_records'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DifferenceStatus.TARGET_ONLY.value
                ).label('target_only_records')
            ).filter(ComparisonResult.task_id == task_id).first()
            
            if stats:
                return {
                    'total_differences': stats.total_differences,
                    'different_records': stats.different_records,
                    'source_only_records': stats.source_only_records,
                    'target_only_records': stats.target_only_records
                }
            return None
    
    # ==================== 高级查询 ====================
    
    def get_user_task_summary(self, user_id: str) -> Dict[str, Any]:
        """获取用户任务摘要 - 展示复杂的关联查询"""
        with self.get_db_session() as session:
            # 复杂的关联查询
            summary = session.query(
                func.count(ComparisonTask.id).label('total_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.COMPLETED.value
                ).label('completed_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.RUNNING.value
                ).label('running_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.FAILED.value
                ).label('failed_tasks'),
                func.avg(ComparisonTask.exec_time).label('avg_execution_time'),
                func.sum(ComparisonTask.diff_records).label('total_differences')
            ).filter(ComparisonTask.user_id == user_id).first()
            
            return {
                'total_tasks': summary.total_tasks or 0,
                'completed_tasks': summary.completed_tasks or 0,
                'running_tasks': summary.running_tasks or 0,
                'failed_tasks': summary.failed_tasks or 0,
                'avg_execution_time': float(summary.avg_execution_time or 0),
                'total_differences': summary.total_differences or 0
            }

    # ==================== 任务管理 ====================

    def _generate_standard_task_name(self) -> str:
        """生成标准化的任务名称"""
        with self.get_db_session() as session:
            # 优化查询，移除冗余条件
            query = text("""
                SELECT task_name FROM comparison_tasks
                WHERE task_name LIKE 'TASK_%%'
                AND LENGTH(task_name) = 8
                ORDER BY task_name DESC
                LIMIT 1
            """)
            result = session.execute(query).fetchone()

            if result and result[0]:
                # 提取序列号并加1
                last_task_name = result[0]
                last_sequence = int(last_task_name.split('_')[1])
                next_sequence = last_sequence + 1
            else:
                # 如果没有找到符合格式的任务，从001开始
                next_sequence = 1

            # 生成新的任务名称，序列号补零到3位
            new_task_name = f"TASK_{next_sequence:03d}"

            # 确保生成的任务名称在数据库中是唯一的
            while session.query(ComparisonTask).filter_by(task_name=new_task_name).first():
                next_sequence += 1
                new_task_name = f"TASK_{next_sequence:03d}"

            return new_task_name

    def create_task_from_model(self, user_name: str, model_id: int, task_name: str = None) -> str:
        """创建比对任务 - 支持标准化任务命名"""
        with self.get_db_session() as session:
            # 验证用户存在，如果不存在则自动创建
            user = self._get_or_create_user(session, user_name)

            # 如果未指定任务名称或为空，则自动生成
            if not task_name or task_name.strip() == "":
                task_name = self._generate_standard_task_name()
            else:
                task_name = task_name.strip()

            # 生成任务ID
            task_id = datetime.now().strftime("A%Y%m%d%H%M%S")

            # 创建任务
            task = ComparisonTask(
                task_id=task_id,
                user_id=user.user_id,
                model_id=model_id,
                task_name=task_name,
                status=TaskStatus.PENDING.value
            )
            session.add(task)
            session.flush()

            logger.info(f"创建比对任务成功: {task_name} ({task_id})")
            return task_id

    def create_task_from_direct(self, user_name: str, task_data) -> str:
        """直接创建任务"""
        with self.get_db_session() as session:
            user = self._get_or_create_user(session, user_name)

            # 提取任务名称，支持标准化命名
            if not hasattr(task_data, 'task_name') or not task_data.task_name or task_data.task_name.strip() == "":
                task_name = self._generate_standard_task_name()
            else:
                task_name = task_data.task_name

            # 生成任务ID
            task_id = datetime.now().strftime("A%Y%m%d%H%M%S")

            # 创建任务
            task = ComparisonTask(
                task_id=task_id,
                user_id=user.user_id,
                model_id=None,
                task_name=task_name,
                status=TaskStatus.PENDING.value
            )
            session.add(task)
            session.flush()

            logger.info(f"TaskCreateDirect任务创建成功: {task_name} ({task_id})")
            return task_id

    def get_user_tasks(self, user_name: str, status_filter: Optional[str] = None,
                      limit: int = 20, offset: int = 0) -> List[ComparisonTask]:
        """获取用户任务列表 - 展示复杂查询功能"""
        with self.get_db_session() as session:
            query = session.query(ComparisonTask).filter_by(user_name=user_name)
            
            # 状态过滤
            if status_filter:
                query = query.filter(ComparisonTask.status == status_filter)
            
            # 预加载关联数据
            query = query.options(
                joinedload(ComparisonTask.user),
                joinedload(ComparisonTask.results)
            )
            
            # 排序和分页
            tasks = query.order_by(desc(ComparisonTask.create_time)).offset(offset).limit(limit).all()
            return tasks
    
    def update_task_status(self, task_id: str, status: TaskStatus, **kwargs) -> bool:
        """更新任务状态"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                return False
            
            # 更新状态
            task.status = status.value
            
            # 更新其他字段
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)
            
            # 设置时间戳（修复字段名）
            if status == TaskStatus.RUNNING and not task.start_time:
                task.start_time = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task.complete_time = datetime.now()
            
            return True

    def get_task_summary(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务汇总信息（从service_models.py迁移的业务逻辑）"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                return None

            # 计算匹配记录数
            matched_records = (task.total_records or 0) - (task.diff_records or 0) - (task.source_only or 0) - (task.target_only or 0)

            return {
                'task_id': task_id,
                'task_name': task.task_name,
                'description': task.description,
                'user_id': task.user_id,
                'model_id': task.model_id,
                'status': task.status,
                'progress_pct': float(task.progress_pct or 0),
                'current_step': task.current_step,
                'create_time': task.create_time,
                'start_time': task.start_time,
                'complete_time': task.complete_time,
                'update_time': task.update_time,
                'total_records': task.total_records or 0,
                'processed_records': task.processed_records or 0,
                'matched_records': max(0, matched_records),
                'diff_records': task.diff_records or 0,
                'source_only': task.source_only or 0,
                'target_only': task.target_only or 0,
                'exec_time': float(task.exec_time or 0),
                'error_msg': task.error_msg,
                'error_details': task.error_details
            }

    def list_connection_configs(self, type: str = None, status: str = None) -> List[Dict[str, Any]]:
        """获取连接配置列表"""
        with self.get_db_session() as session:
            query = session.query(ComparisonConnection)

            if type:
                query = query.filter(ComparisonConnection.type == type.lower())
            if status:
                query = query.filter(ComparisonConnection.status == status)

            connections = query.order_by(desc(ComparisonConnection.create_time)).all()

            return [
                {
                    'id': conn.id,
                    'name': conn.name,
                    'type': conn.type,
                    'host': conn.host,
                    'port': conn.port,
                    'database': conn.database,
                    'status': conn.status,
                    'created_at': conn.create_time.isoformat() if conn.create_time else None
                }
                for conn in connections
            ]

    # ==================== 模型管理方法 ====================

    def create_comparison_model(self, name: str, description: str = None, source_connid: int = None, target_connid: int = None,
                               cmp_type: str = "content", global_config: Dict[str, Any] = None) -> int:
        """创建比对模型"""
        try:
            with self.get_db_session() as session:
                # 检查模型名称是否已存在
                existing_model = session.query(ComparisonModel).filter(ComparisonModel.name == name).first()
                if existing_model:
                    return existing_model.id

                # 验证连接是否存在
                if source_connid:
                    source_conn = session.query(ComparisonConnection).filter(ComparisonConnection.id == source_connid).first()
                    if not source_conn:
                        raise ValueError(f"源连接不存在: {source_connid}")

                if target_connid:
                    target_conn = session.query(ComparisonConnection).filter(ComparisonConnection.id == target_connid).first()
                    if not target_conn:
                        raise ValueError(f"目标连接不存在: {target_connid}")

                # 创建比对模型
                model = ComparisonModel(
                    name=name,
                    description=description,
                    source_connid=source_connid,
                    target_connid=target_connid,
                    cmp_type=cmp_type,
                    global_config=global_config or {}
                )

                session.add(model)
                session.commit()

                logger.info(f"比对模型创建成功: {name}, ID: {model.id}")
                return model.id

        except Exception as e:
            logger.error(f"创建比对模型失败: {e}", exc_info=True)
            raise

    def get_comparison_model(self, model_id: int) -> Optional[Dict[str, Any]]:
        """获取比对模型信息"""
        try:
            with self.get_db_session() as session:
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()

                if not model:
                    return None

                return {
                    'id': model.id,
                    'name': model.name,
                    'description': model.description,
                    'source_connid': model.source_connid,
                    'target_connid': model.target_connid,
                    'cmp_type': model.cmp_type,
                    'global_config': model.global_config,
                    'create_time': model.create_time.isoformat() if model.create_time else None,
                    'update_time': model.update_time.isoformat() if model.update_time else None
                }

        except Exception as e:
            logger.error(f"获取比对模型失败: {model_id}, 错误: {e}")
            return None

    def get_comparison_model_by_name(self, name: str) -> Optional[ComparisonModel]:
        """根据名称查询比对模型"""
        try:
            with self.get_db_session() as session:
                model = session.query(ComparisonModel).filter(ComparisonModel.name == name).first()

                if model:
                    # 将对象从会话中分离，避免会话关闭后访问问题
                    session.expunge(model)

                return model

        except Exception as e:
            logger.error(f"根据名称查询比对模型失败: {name}, 错误: {e}")
            return None

    def create_table_rule(self, model_id: int, table_id: str = None, table_name: str = None,
                         sql_1: str = None, sql_2: str = None, remark: str = None,
                         primary_keys: List[str] = None, ignore_fields: List[str] = None,
                         field_mappings: Dict[str, str] = None) -> int:
        """创建表规则"""
        try:
            with self.get_db_session() as session:
                # 验证模型是否存在
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()
                if not model:
                    raise ValueError(f"比对模型不存在: {model_id}")

                # 如果没有提供table_id，根据表名和remark生成哈希ID
                if not table_id:
                    table_id = self._generate_table_id(model_id, table_name, remark)

                # 检查table_id在同一模型内是否已存在
                existing_rule = session.query(ComparisonTableRule).filter(
                    ComparisonTableRule.model_id == model_id,
                    ComparisonTableRule.table_id == table_id
                ).first()

                if existing_rule:
                    logger.info(f"表规则已存在: {table_id}, ID: {existing_rule.id}")
                    return existing_rule.id

                # 创建表规则
                table_rule = ComparisonTableRule(
                    model_id=model_id,
                    table_id=table_id,
                    table_name=table_name or table_id,
                    sql_1=sql_1,
                    sql_2=sql_2,
                    remark=remark,
                    primary_keys=primary_keys or [],
                    ignore_fields=ignore_fields or [],
                    field_mappings=field_mappings or {}
                )

                session.add(table_rule)
                session.commit()

                logger.info(f"表规则创建成功: {table_name or table_id}, ID: {table_rule.id}")
                return table_rule.id

        except Exception as e:
            logger.error(f"创建表规则失败: {e}", exc_info=True)
            raise

    def _generate_table_id(self, model_id: int, table_name: str, remark: str = None) -> str:
        """根据表名和备注生成唯一的table_id"""
        import hashlib

        # 构建哈希输入字符串
        hash_input = f"{table_name or 'unknown'}"
        if remark:
            hash_input += f"_{remark}"

        # 计算MD5哈希
        hash_object = hashlib.md5(hash_input.encode('utf-8'))
        hash_hex = hash_object.hexdigest().upper()

        # 取前8位作为table_id，并添加模型ID前缀确保跨模型唯一性
        table_id = f"M{model_id}{hash_hex[:8]}"
        return table_id

    def get_table_rule(self, rule_id: int) -> Optional[Dict[str, Any]]:
        """获取表规则信息"""
        try:
            with self.get_db_session() as session:
                rule = session.query(ComparisonTableRule).filter(ComparisonTableRule.id == rule_id).first()

                if not rule:
                    return None

                return {
                    'id': rule.id,
                    'model_id': rule.model_id,
                    'table_id': rule.table_id,
                    'table_name': rule.table_name,
                    'sql_1': rule.sql_1,
                    'sql_2': rule.sql_2,
                    'remark': rule.remark,
                    'primary_keys': rule.primary_keys,
                    'ignore_fields': rule.ignore_fields,
                    'field_mappings': rule.field_mappings,
                    'is_active': rule.is_active,
                    'create_time': rule.create_time.isoformat() if rule.create_time else None,
                    'update_time': rule.update_time.isoformat() if rule.update_time else None
                }

        except Exception as e:
            logger.error(f"获取表规则失败: {rule_id}, 错误: {e}")
            return None

    def get_model_table_rules(self, model_id: int) -> List[Dict[str, Any]]:
        """
        获取模型下的所有表规则

        Args:
            model_id: 模型ID

        Returns:
            List[Dict[str, Any]]: 表规则列表
        """
        try:
            with self.get_db_session() as session:
                rules = session.query(ComparisonTableRule).filter(
                    ComparisonTableRule.model_id == model_id,
                    ComparisonTableRule.is_active == True
                ).all()

                rules_list = []
                for rule in rules:
                    rules_list.append({
                        'id': rule.id,
                        'table_id': rule.table_id,
                        'table_name': rule.table_name,
                        'sql_1': rule.sql_1,
                        'sql_2': rule.sql_2,
                        'remark': rule.remark,
                        'primary_keys': rule.primary_keys,
                        'ignore_fields': rule.ignore_fields,
                        'field_mappings': rule.field_mappings,
                        'is_active': rule.is_active,
                        'create_time': rule.create_time.isoformat() if rule.create_time else None,
                        'update_time': rule.update_time.isoformat() if rule.update_time else None
                    })

                return rules_list

        except Exception as e:
            logger.error(f"获取模型表规则失败: {model_id}, 错误: {e}")
            return []

    # ==================== 删除操作方法 ====================

    def delete_task(self, task_id: str) -> bool:
        """删除任务及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找任务
                task = session.query(ComparisonTask).filter(ComparisonTask.task_id == task_id).first()

                if not task:
                    logger.warning(f"任务不存在: {task_id}")
                    return False

                # 如果任务正在运行，先更新状态为取消
                if task.status == TaskStatus.RUNNING:
                    task.status = TaskStatus.CANCELLED
                    task.update_time = datetime.now()
                    session.commit()
                    logger.info(f"任务 {task_id} 已取消")

                # 级联删除会自动删除相关的ComparisonResult记录
                session.delete(task)
                session.commit()

                logger.info(f"任务删除成功: {task_id}")
                return True

        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, 错误: {e}", exc_info=True)
            return False

    def delete_user(self, user_id: str) -> bool:
        """删除用户及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找用户
                user = session.query(User).filter(User.user_id == user_id).first()

                if not user:
                    logger.warning(f"用户不存在: {user_id}")
                    return False

                # 级联删除会自动删除相关的任务、会话等记录
                session.delete(user)
                session.commit()

                logger.info(f"用户删除成功: {user_id}")
                return True

        except Exception as e:
            logger.error(f"删除用户失败: {user_id}, 错误: {e}", exc_info=True)
            return False

    def delete_comparison_model(self, model_id: int) -> bool:
        """删除比对模型及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找模型
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()

                if not model:
                    logger.warning(f"比对模型不存在: {model_id}")
                    return False

                # 删除模型（级联删除会自动删除相关的表规则和任务）
                session.delete(model)
                session.commit()

                logger.info(f"比对模型删除成功: {model_id}")
                return True

        except Exception as e:
            logger.error(f"删除比对模型失败: {model_id}, 错误: {e}", exc_info=True)
            return False

    def delete_database_connection(self, connection_id: int) -> bool:
        """删除数据库连接配置"""
        try:
            with self.get_db_session() as session:
                # 查找连接
                connection = session.query(ComparisonConnection).filter(
                    ComparisonConnection.id == connection_id
                ).first()

                if not connection:
                    logger.warning(f"数据库连接不存在: {connection_id}")
                    return False

                # 检查是否有模型在使用此连接
                models_using_connection = session.query(ComparisonModel).filter(
                    (ComparisonModel.source_connid == connection_id) |
                    (ComparisonModel.target_connid == connection_id)
                ).count()

                if models_using_connection > 0:
                    logger.warning(f"无法删除连接 {connection_id}: 仍有 {models_using_connection} 个模型在使用")
                    return False

                # 删除连接
                session.delete(connection)
                session.commit()

                logger.info(f"数据库连接删除成功: {connection_id}")
                return True

        except Exception as e:
            logger.error(f"删除数据库连接失败: {connection_id}, 错误: {e}", exc_info=True)
            return False

    def delete_table_rule(self, rule_id: int) -> bool:
        """删除表规则"""
        try:
            with self.get_db_session() as session:
                # 查找规则
                rule = session.query(ComparisonTableRule).filter(ComparisonTableRule.id == rule_id).first()

                if not rule:
                    logger.warning(f"表规则不存在: {rule_id}")
                    return False

                # 级联删除会自动处理相关任务
                session.delete(rule)
                session.commit()

                logger.info(f"表规则删除成功: {rule_id}")
                return True

        except Exception as e:
            logger.error(f"删除表规则失败: {rule_id}, 错误: {e}", exc_info=True)
            return False

    def cleanup_old_tasks(self, days_old: int = 30, status_filter: List[str] = None) -> int:
        """清理旧任务数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)

            with self.get_db_session() as session:
                query = session.query(ComparisonTask).filter(ComparisonTask.create_time < cutoff_date)

                # 应用状态过滤器
                if status_filter:
                    query = query.filter(ComparisonTask.status.in_(status_filter))
                else:
                    # 默认只删除已完成、失败或取消的任务
                    query = query.filter(ComparisonTask.status.in_([
                        TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED
                    ]))

                # 获取要删除的任务
                old_tasks = query.all()
                deleted_count = len(old_tasks)

                if deleted_count == 0:
                    logger.info("没有找到需要清理的旧任务")
                    return 0

                # 批量删除
                for task in old_tasks:
                    session.delete(task)

                session.commit()

                logger.info(f"清理旧任务完成: 删除了 {deleted_count} 个任务")
                return deleted_count

        except Exception as e:
            logger.error(f"清理旧任务失败: {e}", exc_info=True)
            return 0
