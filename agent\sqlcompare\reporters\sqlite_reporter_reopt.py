#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite报告器 - 精简优化版本

混合架构设计：
- SQLAlchemy：用于数据库初始化、表结构管理、查询操作
- 原生 sqlite3：专门用于 comparison_results 表的高频插入操作
"""
import os
import time
import sqlite3
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

# 使用统一的数据模型和服务
from services.sqlalchemy_service import SQLAlchemyComparisonService
from reporters.base_reporter import BaseReporter
from core.models import DiffResult

# 配置日志
logger = logging.getLogger(__name__)


class HighPerformanceBatchWriter:
    """高性能批处理写入器 - 专注于插入性能"""

    def __init__(self, service: SQLAlchemyComparisonService, config: Dict[str, Any]):
        self.service = service
        self.batch_size = config.get('batch_size', 10000)
        self.buffer = []
        self._lock = threading.Lock()
        self.total_records = 0
        
        # 事务管理
        self.transaction_batch_size = config.get('transaction_batch_size', 5)
        self._transaction_active = False
        self._batch_count = 0
        
        # 初始化原生SQLite连接
        self._init_native_sqlite_connection(config)

    def _init_native_sqlite_connection(self, config):
        """初始化原生SQLite连接"""
        try:
            # 从SQLAlchemy服务获取数据库路径
            db_url = str(self.service.engine.url)
            if db_url.startswith('sqlite:///'):
                self.db_path = db_url[10:]
            else:
                self.db_path = config.get('db_path', 'sqlcompare_v4.db')

            # 创建原生SQLite连接
            self.native_conn = sqlite3.connect(
                self.db_path, 
                check_same_thread=False,
                timeout=30.0
            )
            
            # 性能优化设置
            self.native_conn.execute("PRAGMA journal_mode=WAL")
            self.native_conn.execute("PRAGMA synchronous=NORMAL")
            self.native_conn.execute("PRAGMA cache_size=50000")
            self.native_conn.execute("PRAGMA temp_store=MEMORY")
            
            if config.get('high_performance_mode', True):
                self.native_conn.execute("PRAGMA cache_size=100000")
                
            self.native_conn.commit()
            
            # 预编译插入语句
            self.insert_sql = """
                INSERT INTO comparison_results 
                (task_id, table_name, record_key, status, field_name, source_value, target_value, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            logger.info(f"原生SQLite连接初始化完成: {self.db_path}")

        except Exception as e:
            logger.error(f"原生SQLite连接初始化失败: {e}")
            raise

    def add_record(self, record: Dict[str, Any]):
        """添加记录到批处理缓冲区"""
        with self._lock:
            self.buffer.append(record)
            self.total_records += 1

            # 检查是否需要刷新批处理
            if len(self.buffer) >= self.batch_size:
                self._flush_batch()

    def _flush_batch(self):
        """执行批量插入"""
        if not self.buffer:
            return

        try:
            # 准备批量数据
            batch_data = []
            current_time = datetime.now().isoformat()
            
            for record in self.buffer:
                batch_data.append((
                    record.get('task_id'),
                    record.get('table_name'),
                    record.get('record_key'),
                    record.get('status'),
                    record.get('field_name'),
                    record.get('source_value'),
                    record.get('target_value'),
                    current_time
                ))
            
            # 事务管理
            if not self._transaction_active:
                self.native_conn.execute("BEGIN IMMEDIATE")
                self._transaction_active = True
            
            # 批量插入
            cursor = self.native_conn.cursor()
            cursor.executemany(self.insert_sql, batch_data)
            
            # 增加批次计数
            self._batch_count += 1
            
            # 根据批次数量决定是否提交事务
            if self._batch_count >= self.transaction_batch_size:
                self.native_conn.commit()
                self._transaction_active = False
                self._batch_count = 0
            
            # 清空缓冲区
            self.buffer.clear()
            
        except Exception as e:
            logger.error(f"批量插入失败: {e}")
            # 回滚事务
            if self._transaction_active:
                self.native_conn.rollback()
                self._transaction_active = False
                self._batch_count = 0
            self.buffer.clear()
            raise

    def force_flush(self):
        """强制刷新所有缓冲的记录"""
        with self._lock:
            if self.buffer:
                self._flush_batch()
        
        # 确保事务提交
        if self._transaction_active:
            self.native_conn.commit()
            self._transaction_active = False
            self._batch_count = 0

    def close(self):
        """关闭批处理器"""
        try:
            # 强制刷新剩余数据
            self.force_flush()
            
            # 关闭原生SQLite连接
            if hasattr(self, 'native_conn'):
                self.native_conn.close()
                logger.debug("原生SQLite连接已关闭")
                
        except Exception as e:
            logger.error(f"关闭批处理器时出错: {e}")


class SqliteReporter(BaseReporter):
    """SQLite报告器 - 精简优化版本"""

    def __init__(self, config: Dict[str, Any]):
        """初始化SQLite报告器"""
        super().__init__(config)

        # 基础配置
        self.db_path = config.get('db_path', 'sqlcompare_v4.db')
        self.task_id = config.get('task_id') or f"task_{int(time.time())}"
        
        # 初始化SQLAlchemy服务（用于表结构管理和查询）
        database_url = f"sqlite:///{self.db_path}"
        self.service = SQLAlchemyComparisonService.get_instance(database_url)

        # 初始化高性能批处理器（使用原生sqlite3）
        self.batch_writer = HighPerformanceBatchWriter(self.service, config)

        # 向后兼容的属性
        self.total_records = 0
    
    def open(self):
        """打开报告目标"""
        try:
            # 确保数据库表结构存在
            self.service.ensure_tables_exist()
            logger.info(f"SQLite报告器已打开: {self.db_path}")
        except Exception as e:
            logger.error(f"打开SQLite报告器失败: {e}")
            raise

    def add_difference(self, diff_result: DiffResult):
        """添加差异记录"""
        try:
            # 转换为批处理记录格式
            records = self._convert_diff_to_records(diff_result)
            
            # 添加到批处理器
            for record in records:
                self.batch_writer.add_record(record)
                self.total_records += 1
                
        except Exception as e:
            logger.error(f"添加差异记录失败: {e}")
            raise

    def _convert_diff_to_records(self, diff_result: DiffResult) -> List[Dict[str, Any]]:
        """将差异结果转换为数据库记录格式"""
        base_record = {
            'task_id': self.task_id,
            'table_name': getattr(diff_result, 'table_name', 'unknown'),
            'record_key': str(getattr(diff_result, 'record_key', '')),
            'status': getattr(diff_result, 'status', 'UNKNOWN'),
            'field_name': getattr(diff_result, 'field_name', None),
            'source_value': str(getattr(diff_result, 'source_value', None)) if getattr(diff_result, 'source_value', None) is not None else None,
            'target_value': str(getattr(diff_result, 'target_value', None)) if getattr(diff_result, 'target_value', None) is not None else None
        }
        return [base_record]

    def close(self):
        """关闭报告目标 - 参考refactored版本的高性能关闭方式"""
        try:
            # 提交剩余的批量数据
            if self.batch_writer:
                self.batch_writer.close()

            logger.info(f"SQLite报告器关闭，处理记录: {self.total_records}")

        except Exception as e:
            logger.error(f"关闭SQLite报告器时出错: {e}")

    def get_comparison_results(
        self,
        table_name: str = None,
        status: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int]:
        """查询比较结果"""
        try:
            # 委托给SQLAlchemy服务进行查询
            with self.service.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult
                from sqlalchemy import func

                # 构建查询
                query = session.query(ComparisonResult)
                count_query = session.query(func.count(ComparisonResult.id))

                # 添加过滤条件
                if table_name:
                    query = query.filter(ComparisonResult.table_name == table_name)
                    count_query = count_query.filter(ComparisonResult.table_name == table_name)

                if status:
                    query = query.filter(ComparisonResult.status == status)
                    count_query = count_query.filter(ComparisonResult.status == status)

                # 获取总数
                total_count = count_query.scalar()

                # 分页查询
                results = query.offset(offset).limit(limit).all()

                # 转换为字典格式
                result_dicts = []
                for result in results:
                    result_dict = {
                        'id': result.id,
                        'task_id': result.task_id,
                        'table_name': result.table_name,
                        'record_key': result.record_key,
                        'status': result.status,
                        'field_name': result.field_name,
                        'source_value': result.source_value,
                        'target_value': result.target_value,
                        'created_at': result.created_at.isoformat() if result.created_at else None
                    }
                    result_dicts.append(result_dict)

                return result_dicts, total_count

        except Exception as e:
            logger.error(f"查询比较结果失败: {e}")
            return [], 0

    # ==================== 兼容性方法 ====================

    def create_comparison_task(self, name: str, description: str = None, **kwargs) -> Optional[str]:
        """创建新的比对任务（兼容性方法）"""
        task_id = f"task_{name}_{int(time.time())}"
        logger.info(f"创建比对任务: {name} (ID: {task_id})")
        return task_id

    def start_task(self, task_id: str = None):
        """开始执行任务（兼容性方法）"""
        task_id = task_id or self.task_id
        logger.info(f"开始执行任务: {task_id}")

    def complete_task(self, task_id: str = None, **summary):
        """完成任务（兼容性方法）"""
        task_id = task_id or self.task_id
        logger.info(f"完成任务: {task_id}, 摘要: {summary}")

    def fail_task(self, task_id: str = None, error_msg: str = None):
        """标记任务失败（兼容性方法）"""
        task_id = task_id or self.task_id
        logger.error(f"任务失败: {task_id}, 错误: {error_msg}")