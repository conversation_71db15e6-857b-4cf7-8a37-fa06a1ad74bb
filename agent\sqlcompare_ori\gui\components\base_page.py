#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础页面组件类
"""
import os
import sys
import tkinter as tk
from tkinter import ttk
from abc import ABC, abstractmethod
# 将项目根目录添加到sys.path
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_DIR)
from utils.config_manager import SmartConfigManager


class BasePage(ABC):
    """基础页面组件抽象类"""
    
    def __init__(self, parent, app_instance=None):
        """
        初始化页面组件
        
        Args:
            parent: 父容器
            app_instance: 主应用程序实例，用于访问共享数据和方法
        """
        self.parent = parent
        self.app = app_instance
        self.config_manager = SmartConfigManager()
        self.frame = None
        self._create_ui()
    
    @abstractmethod
    def _create_ui(self):
        """创建用户界面 - 子类必须实现"""
        pass
    
    def show(self):
        """显示页面"""
        if self.frame:
            self.frame.pack(fill=tk.BOTH, expand=True)
        else:
            # 如果frame不存在，重新创建UI
            self._create_ui()
            if self.frame:
                self.frame.pack(fill=tk.BOTH, expand=True)

    def hide(self):
        """隐藏页面"""
        if self.frame:
            self.frame.pack_forget()
    
    def destroy(self):
        """销毁页面"""
        if self.frame:
            self.frame.destroy()
            self.frame = None
    
    def refresh(self):
        """刷新页面数据 - 子类可以重写"""
        pass
    
    def get_data(self):
        """获取页面数据 - 子类可以重写"""
        return {}
    
    def set_data(self, data):
        """设置页面数据 - 子类可以重写"""
        pass
    
    def validate(self):
        """验证页面数据 - 子类可以重写"""
        return True, ""
    
    def _log_message(self, message, level="INFO"):
        """记录日志消息"""
        if self.app and hasattr(self.app, '_log_message'):
            self.app._log_message(message, level)
        else:
            print(f"[{level}] {message}")
    
    def _update_status(self, message):
        """更新状态栏"""
        if self.app and hasattr(self.app, 'update_status_bar'):
            self.app.update_status_bar(status_text=message)
        else:
            print(f"Status: {message}")
