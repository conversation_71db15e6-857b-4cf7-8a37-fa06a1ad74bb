@echo off
echo Building sqlcompare.exe with IBM DB2 support from site-packages...

set CLIDRIVER_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\clidriver

if not exist "%CLIDRIVER_PATH%" (
    echo ERROR: clidriver not found at %CLIDRIVER_PATH%
    echo Please check your ibm_db installation
    pause
    exit /b 1
)

echo Found clidriver at: %CLIDRIVER_PATH%

if exist "%CLIDRIVER_PATH%\bin\db2cli64.dll" (
    echo OK: db2cli64.dll found
) else (
    echo WARNING: db2cli64.dll not found
)

echo Cleaning previous build...
if exist "dist\sqlcompare.exe" del "dist\sqlcompare.exe"
if exist "build" rmdir /s /q "build"

echo Building executable...
pyinstaller -F --add-data "%CLIDRIVER_PATH%;clidriver" --hidden-import=ibm_db --name=sqlcompare main.py

if exist "dist\sqlcompare.exe" (
    echo.
    echo Build successful!
    echo Executable: dist\sqlcompare.exe
    echo.
    echo Testing the executable...
    echo Running: dist\sqlcompare.exe
    echo.
    dist\sqlcompare.exe
) else (
    echo.
    echo Build failed! Check error messages above.
)

pause
